import React from "react";
import { Outlet, useNavigate, useLocation } from "react-router-dom";
import { Button, App } from "antd";
import imgBbsLogo from "@/assets/bbs-menu-logo.png";
import styles from "./index.module.less";

// 图片资源常量
const imgApplicationsIcon =
  "http://localhost:3845/assets/004951a2bba025035b11a26b32a2af3d920c1b1f.svg";
const imgLogoutIcon =
  "http://localhost:3845/assets/401c8086b04bd43546159ad47d2eddc604f87095.svg";
const imgArrowDown =
  "http://localhost:3845/assets/36723968928b848dac5aaea91064687d153d094e.svg";

interface NavigationItemProps {
  icon: string;
  text: string;
  isActive?: boolean;
  onClick?: () => void;
}

const NavigationItem: React.FC<NavigationItemProps> = ({
  icon,
  text,
  isActive = false,
  onClick,
}) => {
  return (
    <div
      className={`${styles.navigationItem} ${isActive ? styles.active : ""}`}
      onClick={onClick}
    >
      <div className={styles.navigationContent}>
        <div className={styles.iconWrapper}>
          <img src={icon} alt={text} className={styles.icon} />
        </div>
        <span className={styles.navigationText}>{text}</span>
      </div>
    </div>
  );
};

interface ProfileProps {
  name?: string;
}

const Profile: React.FC<ProfileProps> = ({ name = "AK" }) => {
  return (
    <div className={styles.profile}>
      <span className={styles.profileText}>{name}</span>
    </div>
  );
};

const MainLayout: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { message } = App.useApp();

  // 根据路由获取页面标题
  const getPageTitle = () => {
    if (location.pathname === "/applications") {
      return "Applications Request";
    } else if (location.pathname.startsWith("/applications/")) {
      return "Application Details";
    }
    return "Applications Request";
  };

  const handleLogout = () => {
    // 清除本地存储的用户信息
    localStorage.removeItem("token");
    localStorage.removeItem("user");

    message.success("退出登录成功");
    navigate("/login");
  };

  const handleNavigateToApplications = () => {
    navigate("/applications");
  };

  const isApplicationsActive = location.pathname === "/applications";

  return (
    <div className={styles.layoutContainer}>
      {/* 侧边导航 */}
      <div className={styles.sideNavigation}>
        <div className={styles.sideContent}>
          {/* Logo */}
          <div className={styles.logoWrapper}>
            <img src={imgBbsLogo} alt="BBS Logo" className={styles.logo} />
          </div>

          {/* 导航菜单 */}
          <div className={styles.navigationMenu}>
            <NavigationItem
              icon={imgApplicationsIcon}
              text="Applications"
              isActive={isApplicationsActive}
              onClick={handleNavigateToApplications}
            />
          </div>
        </div>

        {/* 退出登录 */}
        <div className={styles.logoutWrapper}>
          <NavigationItem
            icon={imgLogoutIcon}
            text="Logout"
            onClick={handleLogout}
          />
        </div>
      </div>

      {/* 主内容区域 */}
      <div className={styles.mainContent}>
        {/* 头部 */}
        <div className={styles.header}>
          <div className={styles.headerContent}>
            <h1 className={styles.pageTitle}>{getPageTitle()}</h1>

            <div className={styles.headerActions}>
              {/* 用户头像 */}
              <Profile name="AK" />

              {/* 语言切换按钮 */}
              <Button className={styles.languageButton}>
                <span>English</span>
                <img
                  src={imgArrowDown}
                  alt="Arrow Down"
                  className={styles.arrowIcon}
                />
              </Button>
            </div>
          </div>
        </div>

        {/* 页面内容 */}
        <div className={styles.pageContent}>
          <Outlet />
        </div>
      </div>
    </div>
  );
};

export default MainLayout;
