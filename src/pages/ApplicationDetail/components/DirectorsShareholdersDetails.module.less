// Directors/Shareholders Details 组件样式
.directorsShareholdersDetails {
  display: flex;
  gap: 32px;
  width: 100%;

  // 左侧内容区域
  .leftContent {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 32px;

    .section {
      .sectionTitle {
        margin: 0 0 24px 0;
        font-family: "Inter", sans-serif;
        font-size: 16px;
        font-weight: 600;
        color: #081021;
        line-height: 24px;
      }

      .cardsContainer {
        display: flex;
        flex-direction: column;
        gap: 32px;

        .personCard {
          background: #ffffff;
          border: 1px solid #e4e7ec;
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0px 1px 8px 0px rgba(25, 33, 61, 0.08);
          width: 100%;

          .cardContent {
            display: flex;
            flex-direction: column;
            gap: 32px;

            // 角色标签
            .roleTags {
              display: flex;
              align-items: center;
              gap: 12px;

              .roleTagsGroup {
                display: flex;
                align-items: center;
                gap: 16px;
              }

              .roleTag {
                padding: 2px 12px;
                border-radius: 12px;
                font-family: "Inter", sans-serif;
                font-size: 12px;
                font-weight: 500;
                line-height: 18px;
                letter-spacing: 0.12px;
                display: flex;
                justify-content: center;
                align-items: center;

                &.directorTag {
                  background: #fbd3fe;
                  color: #6c0075;
                }

                &.shareholderTag {
                  background: #b1d5ff;
                  color: #003675;
                }

                &.uboTag {
                  background: #ffe9d3;
                  color: #6f3800;
                }
              }
            }

            // 基本信息
            .basicInfo {
              display: flex;
              flex-direction: column;
              gap: 24px;

              .infoRow {
                display: flex;
                gap: 32px;
                align-items: flex-start;

                .field {
                  flex: 1;
                  display: flex;
                  flex-direction: column;
                  gap: 6px;

                  .label {
                    font-family: "Inter", sans-serif;
                    font-size: 14px;
                    font-weight: 400;
                    color: #667085;
                    line-height: 18px;
                    letter-spacing: 0.14px;
                  }

                  .value {
                    font-family: "Inter", sans-serif;
                    font-size: 14px;
                    font-weight: 500;
                    color: #081021;
                    line-height: 18px;
                    letter-spacing: 0.14px;
                  }
                }
              }
            }

            // 背景检查
            .backgroundChecks {
              display: flex;
              flex-direction: column;
              gap: 20px;

              .checkRow {
                display: flex;
                gap: 32px;
                align-items: center;

                .checkItem {
                  width: 200px;
                  display: flex;
                  align-items: center;
                  gap: 8px;

                  .checkbox {
                    :global(.ant-checkbox) {
                      .ant-checkbox-inner {
                        width: 16px;
                        height: 16px;
                        border-radius: 4px;
                        background-color: #1b3770;
                        border-color: #1b3770;

                        &::after {
                          border-color: white;
                        }
                      }

                      &.ant-checkbox-checked .ant-checkbox-inner {
                        background-color: #1b3770;
                        border-color: #1b3770;
                      }

                      &.ant-checkbox-disabled.ant-checkbox-checked
                        .ant-checkbox-inner {
                        background-color: #1b3770 !important;
                        border-color: #1b3770 !important;
                        opacity: 1;

                        &::after {
                          border-color: white;
                        }
                      }
                    }
                  }

                  .checkLabel {
                    font-family: "Inter", sans-serif;
                    font-size: 14px;
                    font-weight: 500;
                    color: #204184;
                    line-height: 18px;
                    letter-spacing: 0.14px;
                    text-decoration: underline;
                    flex: 1;
                  }

                  .checkStatus {
                    font-family: "Inter", sans-serif;
                    font-size: 12px;
                    font-weight: 500;
                    color: #079455;
                    line-height: 18px;
                    letter-spacing: 0.12px;
                  }
                }
              }
            }

            // View More 按钮
            .viewMoreSection {
              display: flex;
              justify-content: flex-end;

              .viewMoreButton {
                padding: 8px 16px;
                background: #ffffff;
                border: 1px solid #5871a3;
                border-radius: 10px;
                color: #1b3770;
                font-family: "Inter", sans-serif;
                font-size: 14px;
                font-weight: 600;
                line-height: 18px;
                letter-spacing: 0.14px;
                display: flex;
                align-items: center;
                gap: 8px;

                &:hover {
                  border-color: #1b3770;
                  color: #1b3770;
                }
              }
            }
          }
        }
      }
    }
  }

  // 右侧操作区域
  .rightContent {
    width: 298px;
    display: flex;
    flex-direction: column;
    gap: 24px;

    // 操作按钮
    .actionButtons {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .submitButton {
        background: #204184;
        border-color: #204184;
        border-radius: 10px;
        font-weight: 600;
        font-size: 14px;
        height: 40px;
        box-shadow: 0px 2px 5px 0px rgba(32, 65, 132, 0.25);

        &:hover {
          background: #1b3770;
          border-color: #1b3770;
        }
      }

      .returnButton {
        background: #ffffff;
        border: 1px solid #5871a3;
        color: #1b3770;
        border-radius: 10px;
        font-weight: 600;
        font-size: 14px;
        height: 40px;

        &:hover {
          border-color: #1b3770;
          color: #1b3770;
        }
      }
    }

    // 文档区域
    .documentsSection {
      background: #ffffff;
      border: 1px solid #e4e7ec;
      border-radius: 12px;
      padding: 20px;

      .documentsTitle {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: #081021;
        line-height: 18px;
      }

      .documentsList {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .documentItem {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 6px 8px;
          border-radius: 4px;
          transition: background-color 0.2s ease;

          &:hover {
            background: #f9fafb;
          }

          .documentInfo {
            display: flex;
            align-items: center;
            gap: 6px;
            flex: 1;

            .folderIcon {
              color: #475467;
              font-size: 12px;
            }

            .documentName {
              font-size: 12px;
              font-weight: 400;
              color: #344054;
              line-height: 18px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .downloadButton {
            color: #475467;
            padding: 0;
            width: 16px;
            height: 16px;
            min-width: 16px;
            display: flex;
            align-items: center;
            justify-content: center;

            :global(.anticon) {
              font-size: 12px;
            }

            &:hover {
              color: #204184;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .directorsShareholdersDetails {
    flex-direction: column;
    gap: 24px;

    .rightContent {
      width: 100%;

      .actionButtons {
        flex-direction: row;
        gap: 12px;

        .submitButton,
        .returnButton {
          flex: 1;
        }
      }
    }

    .leftContent {
      gap: 24px;

      .section {
        .cardsContainer {
          gap: 24px;

          .personCard {
            padding: 16px;

            .cardContent {
              gap: 24px;

              .roleTags {
                gap: 8px;

                .roleTagsGroup {
                  gap: 8px;
                }

                .roleTag {
                  font-size: 10px;
                  padding: 1px 8px;
                }
              }

              .basicInfo {
                gap: 16px;

                .infoRow {
                  gap: 16px;

                  .field {
                    .label {
                      font-size: 12px;
                    }

                    .value {
                      font-size: 12px;
                    }
                  }
                }
              }

              .backgroundChecks {
                gap: 16px;

                .checkRow {
                  gap: 16px;
                  flex-wrap: wrap;

                  .checkItem {
                    width: auto;
                    min-width: 120px;

                    .checkLabel {
                      font-size: 12px;
                    }

                    .checkStatus {
                      font-size: 10px;
                    }
                  }
                }
              }

              .viewMoreSection {
                .viewMoreButton {
                  font-size: 12px;
                  padding: 6px 12px;
                }
              }
            }
          }
        }
      }
    }
  }
}
