import React from "react";
import { DownloadOutlined } from "@ant-design/icons";
import type { Application } from "@/types/application";
import styles from "./DocumentsPanel.module.less";

interface DocumentsPanelProps {
  application: Application;
}

const DocumentsPanel: React.FC<DocumentsPanelProps> = ({ application }) => {
  const handleDownload = (documentName: string) => {
    console.log("Download document:", documentName);
  };

  // 模拟文档数据
  const documents = [
    {
      id: "1",
      name: "Compliance Schedule.pdf",
      type: "file",
    },
    {
      id: "2",
      name: "TAN WEI MING (Director,Shareholder,UBO)",
      type: "folder",
    },
    {
      id: "3",
      name: "WON<PERSON> CAI HONG (Director)",
      type: "folder",
    },
    {
      id: "4",
      name: "LEE LAI FA (Shareholder,UBO)",
      type: "folder",
    },
    {
      id: "5",
      name: "ABC SDN.BHD (Corporate Shareholder)",
      type: "folder",
    },
  ];

  return (
    <div className={styles.documentsPanel}>
      {/* 标题栏 */}
      <div className={styles.header}>
        <div className={styles.title}>Documents</div>
        <button className={styles.downloadAllButton}>
          <DownloadOutlined />
        </button>
      </div>

      {/* 文档列表 */}
      <div className={styles.documentsList}>
        {documents.map((doc) => (
          <div key={doc.id} className={styles.documentItem}>
            <div className={styles.documentInfo}>
              <div className={styles.documentIcon}>
                <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
                  <path
                    d="M1.5 2.25C1.5 1.83579 1.83579 1.5 2.25 1.5H6L9.75 5.25V9.75C9.75 10.1642 9.41421 10.5 9 10.5H2.25C1.83579 10.5 1.5 10.1642 1.5 9.75V2.25Z"
                    fill="#667085"
                  />
                </svg>
              </div>
              <div className={styles.documentName}>{doc.name}</div>
            </div>
            {doc.type === "file" && (
              <button
                className={styles.downloadButton}
                onClick={() => handleDownload(doc.name)}
              >
                <DownloadOutlined />
              </button>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DocumentsPanel;
