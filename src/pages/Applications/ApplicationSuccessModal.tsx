import React, { useState } from "react";
import { <PERSON><PERSON>, Button, Input, App } from "antd";
import styles from "./ApplicationSuccessModal.module.less";

// 图片资源常量
const imgCopyIcon = "http://localhost:3845/assets/710cf2c1998165cc6136a91aeb89c3421f11b22a.svg";

interface ApplicationSuccessModalProps {
  visible: boolean;
  onClose: () => void;
  applicationId: string;
}

const ApplicationSuccessModal: React.FC<ApplicationSuccessModalProps> = ({
  visible,
  onClose,
  applicationId,
}) => {
  const { message } = App.useApp();
  
  // 生成应用链接
  const applicationLink = `https://bbstrust.com/application/${applicationId}`;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(applicationLink);
      message.success("Link copied to clipboard!");
    } catch (error) {
      console.error("Failed to copy link:", error);
      // 降级方案：选择文本
      const input = document.createElement("input");
      input.value = applicationLink;
      document.body.appendChild(input);
      input.select();
      document.execCommand("copy");
      document.body.removeChild(input);
      message.success("Link copied to clipboard!");
    }
  };

  const handleClose = () => {
    onClose();
  };

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={600}
      className={styles.applicationSuccessModal}
      destroyOnClose
      maskClosable={false}
    >
      <div className={styles.modalContent}>
        <div className={styles.contentWrapper}>
          {/* 应用链接输入框 */}
          <div className={styles.linkSection}>
            <div className={styles.labelWrapper}>
              <label className={styles.label}>Application Link</label>
            </div>
            <div className={styles.inputWrapper}>
              <div className={styles.inputContent}>
                <div className={styles.linkText}>
                  <a 
                    href={applicationLink} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className={styles.link}
                  >
                    {applicationLink}
                  </a>
                </div>
                <button 
                  className={styles.copyButton}
                  onClick={handleCopyLink}
                  type="button"
                >
                  <img 
                    src={imgCopyIcon} 
                    alt="Copy" 
                    className={styles.copyIcon} 
                  />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 关闭按钮 */}
        <div className={styles.buttonWrapper}>
          <Button
            onClick={handleClose}
            className={styles.closeButton}
          >
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default ApplicationSuccessModal;
