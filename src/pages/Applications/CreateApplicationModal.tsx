import React, { useState, useEffect } from "react";
import { Modal, Form, Input, Select, Button, App } from "antd";
import { getCasePersons, createApplication } from "@/services/application";
import type {
  CreateApplicationRequest,
  CasePersonInCharge,
} from "@/types/application";
import { REGISTRATION_TYPE_OPTIONS } from "@/types/application";
import styles from "./CreateApplicationModal.module.less";

// 图片资源常量
const imgArrowDown =
  "http://localhost:3845/assets/36723968928b848dac5aaea91064687d153d094e.svg";

interface CreateApplicationModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: (applicationId: string) => void;
}

const CreateApplicationModal: React.FC<CreateApplicationModalProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [casePersons, setCasePersons] = useState<CasePersonInCharge[]>([]);
  const [casePersonsLoading, setCasePersonsLoading] = useState(false);
  const { message } = App.useApp();

  // 获取案件负责人列表
  useEffect(() => {
    if (visible) {
      loadCasePersons();
    }
  }, [visible]);

  const loadCasePersons = async () => {
    setCasePersonsLoading(true);
    try {
      const persons = await getCasePersons();
      setCasePersons(persons);
    } catch (error) {
      console.error("Failed to load case persons:", error);
      message.error("Failed to load case persons");
    } finally {
      setCasePersonsLoading(false);
    }
  };

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const requestData: CreateApplicationRequest = {
        registrationType: values.registrationType,
        applicantName: values.applicantName,
        applicantEmail: values.applicantEmail,
        applicantMobile: values.applicantMobile,
        casePersonInChargeId: values.casePersonInChargeId,
      };

      const newApplication = await createApplication(requestData);
      message.success("Application created successfully!");
      form.resetFields();
      onSuccess(newApplication.id);
    } catch (error: any) {
      console.error("Failed to create application:", error);
      message.error(error.message || "Failed to create application");
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  // 案件负责人选项
  const casePersonOptions = casePersons.map((person) => ({
    label: `${person.name} (${person.department})`,
    value: person.id,
  }));

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      className={styles.createApplicationModal}
      destroyOnClose
    >
      <div className={styles.modalContent}>
        <h2 className={styles.modalTitle}>New Registration Request</h2>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className={styles.form}
        >
          <div className={styles.formFields}>
            {/* Registration Type */}
            <Form.Item
              label="Registration Type"
              name="registrationType"
              rules={[
                { required: true, message: "Please select registration type!" },
              ]}
              className={styles.formItem}
            >
              <Select
                placeholder="Select Registration Type"
                className={styles.select}
                suffixIcon={
                  <img
                    src={imgArrowDown}
                    alt="Arrow Down"
                    className={styles.arrowIcon}
                  />
                }
                options={REGISTRATION_TYPE_OPTIONS}
              />
            </Form.Item>

            {/* Case Person in Charge */}
            <Form.Item
              label="Case Person in Charge"
              name="casePersonInChargeId"
              rules={[
                { required: true, message: "Please select person in charge!" },
              ]}
              className={styles.formItem}
            >
              <Select
                placeholder="Select Person in Charge"
                className={styles.select}
                loading={casePersonsLoading}
                suffixIcon={
                  <img
                    src={imgArrowDown}
                    alt="Arrow Down"
                    className={styles.arrowIcon}
                  />
                }
                options={casePersonOptions}
              />
            </Form.Item>

            {/* Applicant Name */}
            <Form.Item
              label="Applicant Name"
              name="applicantName"
              rules={[
                { required: true, message: "Please enter applicant name!" },
                { min: 2, message: "Name must be at least 2 characters!" },
              ]}
              className={styles.formItem}
            >
              <Input placeholder="Enter Name" className={styles.input} />
            </Form.Item>

            {/* Applicant Mobile Number */}
            <Form.Item
              label="Applicant Mobile Number"
              name="applicantMobile"
              rules={[
                { required: true, message: "Please enter mobile number!" },
                {
                  pattern: /^(\+60|60)?[0-9]{9,10}$/,
                  message: "Please enter a valid Malaysian mobile number!",
                },
              ]}
              className={styles.formItem}
            >
              <Input
                placeholder="Enter mobile number"
                className={styles.input}
              />
            </Form.Item>

            {/* Applicant Email */}
            <Form.Item
              label="Applicant Email"
              name="applicantEmail"
              rules={[
                { required: true, message: "Please enter email!" },
                { type: "email", message: "Please enter a valid email!" },
              ]}
              className={styles.formItem}
            >
              <Input placeholder="Enter email" className={styles.input} />
            </Form.Item>
          </div>

          {/* Buttons */}
          <div className={styles.buttonWrapper}>
            <Button
              onClick={handleCancel}
              className={styles.cancelButton}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className={styles.createButton}
            >
              Create
            </Button>
          </div>
        </Form>
      </div>
    </Modal>
  );
};

export default CreateApplicationModal;
