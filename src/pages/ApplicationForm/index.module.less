// 应用填写页面样式 - CSS Modules
.applicationFormPage {
  min-height: 100vh;
  background: #fcfcfe;
  display: flex;
  flex-direction: column;

  .content {
    flex: 1;
    width: 100%;
  }

  .loadingContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: calc(100vh - 93px);
    background: #fcfcfe;
  }

  .container {
    width: 100%;
    max-width: 800px;

    .formCard {
      background: #ffffff;
      border-radius: 12px; // radius-xl
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      border: none;
      overflow: hidden;

      :global(.ant-card-body) {
        padding: 48px 32px;
      }

      :global(.ant-result) {
        .resultIcon {
          color: #204184; // Button/button-primary-bg
          font-size: 72px;
        }

        :global(.ant-result-title) {
          font-family: "Inter", sans-serif;
          font-weight: 600; // semibold
          font-size: 24px; // display-xs
          line-height: 32px; // line-height/display-xs
          color: #081021; // Text/text-primary
          letter-spacing: 0.24px;
        }

        :global(.ant-result-subtitle) {
          font-family: "Inter", sans-serif;
          font-weight: 400; // regular
          font-size: 16px; // text-md
          line-height: 24px; // line-height/text-md
          color: #475467; // Icon/icon-fg-gray
          letter-spacing: 0.16px;
          margin-bottom: 32px;
        }

        :global(.ant-result-extra) {
          margin-top: 32px;

          .infoText {
            margin-bottom: 24px;
            text-align: center;

            p {
              font-family: "Inter", sans-serif;
              font-weight: 400; // regular
              font-size: 14px; // text-sm
              line-height: 20px; // line-height/text-sm
              color: #667085; // Text/text-placeholder
              letter-spacing: 0.14px;
              margin: 8px 0;
            }
          }

          .backButton {
            background-color: #204184; // Button/button-primary-bg
            border: none;
            border-radius: 10px; // radius-lg
            padding: 12px 24px; // spacing-lg spacing-3xl
            height: 48px;
            font-family: "Inter", sans-serif;
            font-weight: 600; // semibold
            font-size: 16px; // text-md
            line-height: 20px; // line-height/text-sm
            color: #ffffff; // Button/button-primary-fg
            letter-spacing: 0.16px;
            box-shadow: 0px 2px 5px 0px rgba(32, 65, 132, 0.25);

            &:hover {
              background-color: #1a3670;
              box-shadow: 0px 4px 8px 0px rgba(32, 65, 132, 0.35);
            }

            &:focus {
              background-color: #204184;
              box-shadow: 0px 2px 5px 0px rgba(32, 65, 132, 0.25),
                0 0 0 2px rgba(32, 65, 132, 0.2);
            }

            &:active {
              background-color: #153059;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .applicationFormPage {
    padding: 16px;

    .container {
      .formCard {
        :global(.ant-card-body) {
          padding: 32px 24px;
        }

        :global(.ant-result) {
          .resultIcon {
            font-size: 56px;
          }

          :global(.ant-result-title) {
            font-size: 20px;
            line-height: 28px;
          }

          :global(.ant-result-subtitle) {
            font-size: 14px;
            line-height: 20px;
          }

          :global(.ant-result-extra) {
            .infoText {
              p {
                font-size: 13px;
                line-height: 18px;
              }
            }

            .backButton {
              width: 100%;
              height: 44px;
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .applicationFormPage {
    padding: 12px;

    .container {
      .formCard {
        :global(.ant-card-body) {
          padding: 24px 16px;
        }

        :global(.ant-result) {
          .resultIcon {
            font-size: 48px;
          }

          :global(.ant-result-title) {
            font-size: 18px;
            line-height: 24px;
          }

          :global(.ant-result-subtitle) {
            font-size: 13px;
            line-height: 18px;
          }
        }
      }
    }
  }
}
