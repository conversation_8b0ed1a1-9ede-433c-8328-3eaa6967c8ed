// SubmitStep 组件样式
.submitStep {
  width: 100%;
  height: 100vh;
  background: #fcfcfe;
  display: flex;
  flex-direction: column;

  .container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 20px;

    .successContent {
      width: 491px;
      max-width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .iconWrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;

        .successCircle {
          width: 57px;
          height: 56px;
          background: #60b527;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .checkIcon {
            color: white;
            font-size: 24px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
          }
        }
      }

      .textContent {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;
        text-align: center;

        .title {
          color: var(--Text-text-primary-(900), #081021);
          font-size: 24px;
          font-family: "Inter", sans-serif;
          font-weight: 600;
          line-height: 32px;
          letter-spacing: 0.24px;
          margin: 0;
          word-wrap: break-word;
        }

        .description {
          color: var(--Text-text-quaternary-(500), #667085);
          font-size: 18px;
          font-family: "Inter", sans-serif;
          font-weight: 400;
          line-height: 24px;
          letter-spacing: 0.18px;
          margin: 0;
          word-wrap: break-word;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .submitStep {
    .container {
      padding: 20px 16px;

      .successContent {
        width: 100%;
        gap: 12px;

        .iconWrapper {
          .successCircle {
            width: 48px;
            height: 48px;

            .checkIcon {
              font-size: 20px;
            }
          }
        }

        .textContent {
          gap: 12px;

          .title {
            font-size: 20px;
            line-height: 28px;
            letter-spacing: 0.20px;
          }

          .description {
            font-size: 16px;
            line-height: 22px;
            letter-spacing: 0.16px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .submitStep {
    .container {
      padding: 16px 12px;

      .successContent {
        .iconWrapper {
          .successCircle {
            width: 40px;
            height: 40px;

            .checkIcon {
              font-size: 18px;
            }
          }
        }

        .textContent {
          .title {
            font-size: 18px;
            line-height: 24px;
          }

          .description {
            font-size: 14px;
            line-height: 20px;
          }
        }
      }
    }
  }
}
