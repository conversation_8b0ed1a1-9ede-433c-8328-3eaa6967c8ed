import React from "react";
import { Form, Input, Select, Row, Col } from "antd";
import type { Rule } from "antd/es/form";

interface FormFieldProps {
  label: string;
  name: string;
  type?: "input" | "select" | "textarea" | "number";
  placeholder?: string;
  rules?: Rule[];
  options?: { value: string; label: string }[];
  rows?: number;
  span?: number;
  suffix?: string;
  min?: number;
  max?: number;
}

const FormField: React.FC<FormFieldProps> = ({
  label,
  name,
  type = "input",
  placeholder,
  rules,
  options,
  rows = 3,
  span = 24,
  suffix,
  min,
  max,
}) => {
  const renderField = () => {
    switch (type) {
      case "select":
        return (
          <Select placeholder={placeholder}>
            {options?.map((option) => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        );
      case "textarea":
        return <Input.TextArea rows={rows} placeholder={placeholder} />;
      case "number":
        return (
          <Input
            type="number"
            min={min}
            max={max}
            placeholder={placeholder}
            suffix={suffix}
          />
        );
      default:
        return <Input placeholder={placeholder} />;
    }
  };

  const fieldElement = (
    <Form.Item label={label} name={name} rules={rules}>
      {renderField()}
    </Form.Item>
  );

  if (span === 24) {
    return fieldElement;
  }

  return <Col span={span}>{fieldElement}</Col>;
};

export default FormField;
