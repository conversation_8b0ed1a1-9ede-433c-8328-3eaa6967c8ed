// OverviewStep 组件样式
.overviewStep {
  width: 100%;
  min-height: calc(100vh - 93px);
  background: #fcfcfe;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;

  .container {
    width: 846px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    gap: 40px;

    .loadingContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      padding: 60px 0;
      color: var(--Text-text-tertiary-(500), #667085);
      font-size: 16px;
      font-family: "Inter", sans-serif;
      font-weight: 400;
      line-height: 20px;
    }

    .content {
      display: flex;
      flex-direction: column;
      gap: 40px;

      .section {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .title {
          padding-left: 24px;
          display: flex;
          justify-content: center;
          align-items: center;

          h2 {
            width: 846px;
            color: var(--Text-text-primary-(900), #081021);
            font-size: 20px;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            line-height: 24px;
            letter-spacing: 0.20px;
            margin: 0;
          }
        }

        .card {
          padding: 24px;
          background: var(--Background-bg-primary, white);
          box-shadow: 0px 1px 8px rgba(24.73, 32.61, 60.56, 0.08);
          border-radius: 12px;
          border: 1px solid var(--Border-border-secondary, #e4e7ec);

          .cardContent {
            display: flex;
            flex-direction: column;
            gap: 32px;

            .optionGroup,
            .detailsGroup {
              display: flex;
              flex-direction: column;
              gap: 12px;
            }

            .row {
              display: flex;
              gap: 24px;

              .field {
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 6px;

                .label {
                  color: var(--Foreground-fg-quaternary-(500), #667085);
                  font-size: 14px;
                  font-family: "Inter", sans-serif;
                  font-weight: 400;
                  line-height: 18px;
                  letter-spacing: 0.14px;
                }

                .value {
                  color: var(--Text-text-primary-(900), #081021);
                  font-size: 14px;
                  font-family: "Inter", sans-serif;
                  font-weight: 400;
                  line-height: 18px;
                  letter-spacing: 0.14px;
                }
              }
            }

            .fullWidthField {
              display: flex;
              flex-direction: column;
              gap: 6px;

              .label {
                color: var(--Foreground-fg-quaternary-(500), #667085);
                font-size: 14px;
                font-family: "Inter", sans-serif;
                font-weight: 400;
                line-height: 18px;
                letter-spacing: 0.14px;
              }

              .value {
                color: var(--Text-text-primary-(900), #081021);
                font-size: 14px;
                font-family: "Inter", sans-serif;
                font-weight: 400;
                line-height: 18px;
                letter-spacing: 0.14px;
              }
            }

            .divider {
              height: 0;
              border-top: 1px solid var(--Border-border-tertiary, #f2f4f7);
            }
          }
        }

        .shareholderWrapper {
          display: flex;
          flex-direction: column;
          gap: 32px;

          .shareholderCard {
            padding: 24px;
            background: var(--Background-bg-primary, white);
            box-shadow: 0px 1px 8px rgba(24.73, 32.61, 60.56, 0.08);
            border-radius: 12px;
            border: 1px solid var(--Border-border-secondary, #e4e7ec);
            display: flex;
            flex-direction: column;
            gap: 32px;

            .shareholderContent {
              display: flex;
              flex-direction: column;
              gap: 12px;

              .roleTags {
                display: flex;
                gap: 12px;
                align-items: center;
              }

              .shareholderInfo {
                display: flex;
                flex-direction: column;
                gap: 24px;

                .row {
                  display: flex;
                  gap: 32px;

                  .field {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    gap: 6px;

                    .label {
                      color: var(--Foreground-fg-quaternary-(500), #667085);
                      font-size: 14px;
                      font-family: "Inter", sans-serif;
                      font-weight: 400;
                      line-height: 18px;
                      letter-spacing: 0.14px;
                    }

                    .value {
                      color: var(--Text-text-primary-(900), #081021);
                      font-size: 14px;
                      font-family: "Inter", sans-serif;
                      font-weight: 500;
                      line-height: 18px;
                      letter-spacing: 0.14px;
                    }
                  }
                }
              }
            }

            .viewMoreSection {
              display: flex;
              justify-content: flex-end;

              .viewMoreButton {
                padding: 8px 16px;
                background: var(--Button-button-secondary-color-bg, white);
                border-radius: 10px;
                border: 1px solid var(--Button-button-secondary-color-border, #5871a3);
                color: var(--Button-button-secondary-color-fg, #1b3770);
                font-size: 14px;
                font-family: "Inter", sans-serif;
                font-weight: 600;
                line-height: 18px;
                letter-spacing: 0.14px;
                display: flex;
                align-items: center;
                gap: 8px;

                &:hover {
                  background: var(--Button-button-secondary-color-bg-hover, #f8f9fa);
                  border-color: var(--Button-button-secondary-color-border-hover, #4a6491);
                }
              }
            }
          }
        }
      }
    }

    .buttonGroup {
      display: flex;
      justify-content: space-between;
      gap: 16px;
      padding-top: 32px;

      .backButton,
      .nextButton {
        height: 48px;
        border-radius: 10px;
        font-size: 16px;
        font-family: "Inter", sans-serif;
        font-weight: 500;
        line-height: 20px;
        letter-spacing: 0.16px;
        padding: 0 32px;
      }

      .backButton {
        background: var(--Button-button-secondary-color-bg, white);
        border: 1px solid var(--Button-button-secondary-color-border, #5871a3);
        color: var(--Button-button-secondary-color-fg, #1b3770);

        &:hover {
          background: var(--Button-button-secondary-color-bg-hover, #f8f9fa);
          border-color: var(--Button-button-secondary-color-border-hover, #4a6491);
        }
      }

      .nextButton {
        background: var(--Button-button-primary-color-bg, #1b3770);
        border: 1px solid var(--Button-button-primary-color-border, #1b3770);
        color: var(--Button-button-primary-color-fg, white);

        &:hover {
          background: var(--Button-button-primary-color-bg-hover, #15305f);
          border-color: var(--Button-button-primary-color-border-hover, #15305f);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .overviewStep {
    padding: 20px 16px;

    .container {
      width: 100%;

      .content {
        .section {
          .title {
            padding-left: 0;

            h2 {
              width: 100%;
              font-size: 18px;
              line-height: 22px;
            }
          }

          .card {
            padding: 20px;

            .cardContent {
              gap: 24px;

              .row {
                flex-direction: column;
                gap: 16px;
              }
            }
          }

          .shareholderWrapper {
            gap: 24px;

            .shareholderCard {
              padding: 20px;

              .shareholderContent {
                .shareholderInfo {
                  .row {
                    flex-direction: column;
                    gap: 16px;
                  }
                }
              }
            }
          }
        }
      }

      .buttonGroup {
        flex-direction: column;

        .backButton,
        .nextButton {
          width: 100%;
          font-size: 14px;
          height: 44px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .overviewStep {
    padding: 16px 12px;

    .container {
      .content {
        gap: 32px;

        .section {
          gap: 12px;

          .title {
            h2 {
              font-size: 16px;
              line-height: 20px;
            }
          }

          .card {
            padding: 16px;
          }

          .shareholderWrapper {
            .shareholderCard {
              padding: 16px;
            }
          }
        }
      }
    }
  }
}
