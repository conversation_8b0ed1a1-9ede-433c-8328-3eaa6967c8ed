import React, { useState } from "react";
import { Form, Button, Radio, Space, Row } from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import type { IndividualShareholderFormData } from "@/types/applicationForm";
import FormField from "./FormField";
import PersonalInfoForm from "./PersonalInfoForm";
import RoleSelector from "./RoleSelector";
import PEPStatusSelector from "./PEPStatusSelector";
import styles from "./ShareholderDetailStep.module.less";

interface IndividualShareholderStepProps {
  onNext: (data: IndividualShareholderFormData) => void;
  onBack: () => void;
  initialData?: IndividualShareholderFormData;
  shareholderName?: string;
}

const IndividualShareholderStep: React.FC<IndividualShareholderStepProps> = ({
  onNext,
  onBack,
  initialData,
  shareholderName = "TAN WEI MING",
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      const formData: IndividualShareholderFormData = {
        firstName: values.firstName,
        lastName: values.lastName,
        nricPassport: values.nricPassport,
        dateOfBirth: values.dateOfBirth,
        nationality: values.nationality,
        address: values.address,
        phoneNumber: values.phoneNumber,
        email: values.email,
        shareholdingPercentage: values.shareholdingPercentage,
        isDirector: values.roles?.includes("director") || false,
        isUBO: values.roles?.includes("ubo") || false,
      };
      onNext(formData);
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.shareholderDetailStep}>
      <div className={styles.container}>
        <div className={styles.header}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
            className={styles.backButton}
          >
            Back to List
          </Button>
          <h1 className={styles.title}>{shareholderName} - KYC Details</h1>
        </div>

        <div className={styles.formContainer}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={initialData}
            className={styles.form}
          >
            {/* Basic Information */}
            <PersonalInfoForm showPlaceOfBirth={true} />

            {/* Role in Company */}
            <RoleSelector />

            {/* Contact Information */}
            <FormField
              label="Residential Address"
              name="address"
              type="textarea"
              placeholder="Enter full residential address"
              rules={[
                { required: true, message: "Please enter residential address" },
              ]}
            />

            <Row gutter={24}>
              <FormField
                label="Phone Number"
                name="phoneNumber"
                placeholder="Enter phone number"
                rules={[
                  { required: true, message: "Please enter phone number" },
                ]}
                span={12}
              />
              <FormField
                label="Email Address"
                name="email"
                placeholder="Enter email address"
                rules={[
                  { required: true, message: "Please enter email address" },
                  {
                    type: "email",
                    message: "Please enter valid email address",
                  },
                ]}
                span={12}
              />
            </Row>

            {/* Shareholding Information */}
            <Row gutter={24}>
              <FormField
                label="Shareholding Percentage"
                name="shareholdingPercentage"
                type="number"
                min={0}
                max={100}
                placeholder="Enter percentage"
                suffix="%"
                rules={[
                  {
                    required: true,
                    message: "Please enter shareholding percentage",
                  },
                ]}
                span={12}
              />
              <FormField
                label="Occupation"
                name="occupation"
                placeholder="Enter occupation"
                rules={[{ required: true, message: "Please enter occupation" }]}
                span={12}
              />
            </Row>

            {/* Employment Status */}
            <Form.Item
              label="Employment Status"
              name="employmentStatus"
              rules={[
                { required: true, message: "Please select employment status" },
              ]}
            >
              <Radio.Group>
                <Space direction="vertical">
                  <Radio value="employed">Employed</Radio>
                  <Radio value="self-employed">Self-employed</Radio>
                  <Radio value="unemployed">Unemployed</Radio>
                  <Radio value="retired">Retired</Radio>
                </Space>
              </Radio.Group>
            </Form.Item>

            {/* PEP Status */}
            <PEPStatusSelector entityType="individual" />

            {/* Submit Button */}
            <Form.Item className={styles.submitSection}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                className={styles.submitButton}
              >
                Done
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default IndividualShareholderStep;
