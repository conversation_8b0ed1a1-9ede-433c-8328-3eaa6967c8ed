// StartStep 组件样式
.startStep {
  width: 100%;
  min-height: calc(100vh - 93px);
  background: #FCFCFE;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;

  .container {
    width: 743px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    gap: 32px;

    .checklistCard {
      width: 100%;
      padding: 40px;
      background: var(--Background-bg-primary, white);
      box-shadow: 0px 1px 8px rgba(24.73, 32.61, 60.56, 0.08);
      border-radius: 12px;
      border: 1px solid var(--Border-border-secondary, #E4E7EC);
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      gap: 20px;

      .checklistTitle {
        color: black;
        font-size: 16px;
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 0.16px;
        word-wrap: break-word;
      }

      .checklistItems {
        width: 100%;
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .noticeSection {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        gap: 4px;

        .noticeText {
          width: 100%;
          color: var(--Text-text-tertiary-(600), #475467);
          font-size: 12px;
          font-family: 'Inter', sans-serif;
          font-weight: 400;
          line-height: 18px;
          letter-spacing: 0.12px;
          word-wrap: break-word;
        }

        .contactText {
          width: 100%;
          color: var(--Text-text-tertiary-(600), #475467);
          font-size: 12px;
          font-family: 'Inter', sans-serif;
          font-weight: 400;
          line-height: 18px;
          letter-spacing: 0.12px;
          word-wrap: break-word;

          .emailLink {
            color: #0052F1;
            font-weight: 500;
            text-decoration: underline;
            
            &:hover {
              color: #003DC7;
            }
          }
        }
      }
    }

    .startButton {
      width: 100%;
      padding: 12px 20px;
      background: var(--Button-button-primary-bg, #204184);
      box-shadow: 0px 3px 4px rgba(223.02, 237.74, 255, 0.10) inset;
      border-radius: 10px;
      border: none;
      height: auto;
      font-size: 16px;
      font-family: 'Inter', sans-serif;
      font-weight: 600;
      line-height: 20px;
      letter-spacing: 0.16px;

      &:hover {
        background: #1a3670 !important;
      }

      &:focus {
        background: #1a3670 !important;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .startStep {
    padding: 20px 16px;

    .container {
      width: 100%;
      gap: 24px;

      .checklistCard {
        padding: 24px;
        gap: 16px;

        .checklistTitle {
          font-size: 14px;
          line-height: 18px;
        }

        .checklistItems {
          gap: 16px;
        }
      }

      .startButton {
        font-size: 14px;
        padding: 10px 16px;
      }
    }
  }
}

@media (max-width: 480px) {
  .startStep {
    .container {
      .checklistCard {
        padding: 20px;

        .noticeSection {
          .noticeText,
          .contactText {
            font-size: 11px;
            line-height: 16px;
          }
        }
      }
    }
  }
}
