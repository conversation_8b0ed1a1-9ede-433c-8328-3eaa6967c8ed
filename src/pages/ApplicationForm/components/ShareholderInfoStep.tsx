import React, { useState, useEffect } from "react";
import { Button, Spin, Tag, Avatar, App } from "antd";
import {
  UserOutlined,
  HomeOutlined,
  DeleteOutlined,
  EditOutlined,
} from "@ant-design/icons";
import {
  type ShareholderInfoFormData,
  ShareholderType,
} from "@/types/applicationForm";
import { getShareholderData } from "@/services/applicationForm";
import styles from "./ShareholderInfoStep.module.less";

interface ShareholderInfoStepProps {
  onNext: (data: ShareholderInfoFormData) => void;
  onBack: () => void;
  onEditShareholder: (shareholderId: string, type: ShareholderType) => void;
  initialData?: ShareholderInfoFormData;
}

interface ShareholderCard {
  id: string;
  type: ShareholderType;
  status: "processing" | "completed" | "error";
  data?: any;
  errorMessage?: string;
  completion?: number;
}

const ShareholderInfoStep: React.FC<ShareholderInfoStepProps> = ({
  onNext,
  onBack,
  onEditShareholder,
  initialData,
}) => {
  const { message } = App.useApp();
  const [shareholders, setShareholders] = useState<ShareholderCard[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadShareholderData();
  }, []);

  const loadShareholderData = async () => {
    try {
      setLoading(true);
      const data = await getShareholderData();
      setShareholders(data);
    } catch (error) {
      message.error("Failed to load shareholder data");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteShareholder = (shareholderId: string) => {
    setShareholders((prev) => prev.filter((s) => s.id !== shareholderId));
  };

  const handleCompleteDetails = (
    shareholderId: string,
    type: ShareholderType
  ) => {
    onEditShareholder(shareholderId, type);
  };

  const handleNext = () => {
    const formData: ShareholderInfoFormData = {
      shareholders: shareholders,
    };
    onNext(formData);
  };

  const renderShareholderCard = (shareholder: ShareholderCard) => {
    const { id, type, status, data, errorMessage, completion } = shareholder;

    if (status === "processing") {
      return (
        <div key={id} className={styles.shareholderCard}>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            className={styles.deleteButton}
            onClick={() => handleDeleteShareholder(id)}
          />
          <div className={styles.processingContent}>
            <Spin size="large" />
            <div className={styles.processingText}>
              OCR recognition processing...
            </div>
          </div>
        </div>
      );
    }

    if (status === "error") {
      return (
        <div key={id} className={styles.shareholderCard}>
          <Button
            type="text"
            icon={<DeleteOutlined />}
            className={styles.deleteButton}
            onClick={() => handleDeleteShareholder(id)}
          />
          <div className={styles.errorContent}>
            <div className={styles.fileName}>image 1.jpeg</div>
            <div className={styles.errorMessage}>
              {errorMessage || "error message"}
            </div>
          </div>
        </div>
      );
    }

    // Completed status
    return (
      <div key={id} className={styles.shareholderCard}>
        <Button
          type="text"
          icon={<DeleteOutlined />}
          className={styles.deleteButton}
          onClick={() => handleDeleteShareholder(id)}
        />

        <div className={styles.cardContent}>
          <div className={styles.avatarSection}>
            <Avatar
              size={72}
              icon={
                type === ShareholderType.INDIVIDUAL ? (
                  <UserOutlined />
                ) : (
                  <HomeOutlined />
                )
              }
              className={styles.avatar}
            />
          </div>

          {type === ShareholderType.INDIVIDUAL && (
            <div className={styles.tags}>
              <Tag color="blue" className={styles.shareholderTag}>
                Shareholder
              </Tag>
              <Tag color="orange" className={styles.uboTag}>
                UBO
              </Tag>
            </div>
          )}

          <div className={styles.infoSection}>
            {type === ShareholderType.INDIVIDUAL ? (
              <>
                <div className={styles.infoItem}>
                  <div className={styles.label}>
                    First Name
                    <EditOutlined className={styles.editIcon} />
                  </div>
                  <div className={styles.value}>
                    {data?.firstName || "FirstName"}
                  </div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.label}>
                    Last Name
                    <EditOutlined className={styles.editIcon} />
                  </div>
                  <div className={styles.value}>
                    {data?.lastName || "LastName"}
                  </div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.label}>NRIC/Passport</div>
                  <div className={styles.value}>
                    {data?.nricPassport || "999999-99-99999"}
                  </div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.label}>Date of Birth</div>
                  <div className={styles.value}>
                    {data?.dateOfBirth || "13 SEP 1992"}
                  </div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.label}>Nationality</div>
                  <div className={styles.value}>
                    {data?.nationality || "MALAYSIA"}
                  </div>
                </div>
              </>
            ) : (
              <>
                <div className={styles.infoItem}>
                  <div className={styles.label}>Name of the Corporate</div>
                  <div className={styles.value}>
                    {data?.corporateName || "COMPANY A sdn.bhd"}
                  </div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.label}>Entity No</div>
                  <div className={styles.value}>
                    {data?.entityNo || "1231233"}
                  </div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.label}>Date on Incorporation</div>
                  <div className={styles.value}>
                    {data?.incorporationDate || "13 SEP 1992"}
                  </div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.label}>
                    First Name
                    <EditOutlined className={styles.editIcon} />
                  </div>
                  <div className={styles.value}>
                    {data?.firstName || "FirstName"}
                  </div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.label}>
                    Last Name
                    <EditOutlined className={styles.editIcon} />
                  </div>
                  <div className={styles.value}>
                    {data?.lastName || "LastName"}
                  </div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.label}>NRIC/Passport</div>
                  <div className={styles.value}>
                    {data?.nricPassport || "999999-99-99999"}
                  </div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.label}>Date of Birth</div>
                  <div className={styles.value}>
                    {data?.dateOfBirth || "13 SEP 1992"}
                  </div>
                </div>
                <div className={styles.infoItem}>
                  <div className={styles.label}>Nationality</div>
                  <div className={styles.value}>
                    {data?.nationality || "MALAYSIA"}
                  </div>
                </div>
              </>
            )}
          </div>
        </div>

        <div className={styles.buttonSection}>
          <div className={styles.completionText}>
            Completion {completion || 0}%
          </div>
          <Button
            type={completion === 100 ? "default" : "primary"}
            size="large"
            onClick={() => handleCompleteDetails(id, type)}
            className={styles.actionButton}
          >
            {completion === 100 ? "View Details" : "Complete Details"}
          </Button>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className={styles.shareholderInfoStep}>
        <div className={styles.container}>
          <div className={styles.loadingContainer}>
            <Spin size="large" />
            <div>Loading shareholder information...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.shareholderInfoStep}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>
            Complete details for all directors, shareholders, and ultimate
            beneficial owners (UBOs).
          </h1>
        </div>

        <div className={styles.shareholderGrid}>
          {shareholders.map(renderShareholderCard)}
        </div>

        <div className={styles.buttonGroup}>
          <Button
            type="default"
            size="large"
            onClick={onBack}
            className={styles.backButton}
          >
            Back
          </Button>
          <Button
            type="primary"
            size="large"
            onClick={handleNext}
            className={styles.nextButton}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ShareholderInfoStep;
