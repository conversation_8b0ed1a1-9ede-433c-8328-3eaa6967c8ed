import React from 'react';
import { Button } from 'antd';
import ChecklistItem from './ChecklistItem';
import styles from './StartStep.module.less';

interface StartStepProps {
  onNext: () => void;
}

const StartStep: React.FC<StartStepProps> = ({ onNext }) => {
  // 图标组件
  const FileIcon = () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.5 1.70215V4.80005C10.5 5.22009 10.5 5.43011 10.5817 5.59055C10.6537 5.73167 10.7684 5.8464 10.9095 5.91831C11.0699 6.00005 11.28 6.00005 11.7 6.00005H14.7979M12 9.75H6M12 12.75H6M7.5 6.75H6M10.5 1.5H6.6C5.33988 1.5 4.70982 1.5 4.22852 1.74524C3.80516 1.96095 3.46095 2.30516 3.24524 2.72852C3 3.20982 3 3.83988 3 5.1V12.9C3 14.1601 3 14.7902 3.24524 15.2715C3.46095 15.6948 3.80516 16.039 4.22852 16.2548C4.70982 16.5 5.33988 16.5 6.6 16.5H11.4C12.6601 16.5 13.2902 16.5 13.7715 16.2548C14.1948 16.039 14.539 15.6948 14.7548 15.2715C15 14.7902 15 14.1601 15 12.9V6L10.5 1.5Z" stroke="var(--Foreground-fg-quinary--400-, #98A2B3)" strokeLinecap="round" strokeLinejoin="round"/>
    </svg>
  );

  const FileAttachmentIcon = () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M10.6875 13.5V10.5C10.6875 10.1893 10.9393 9.9375 11.25 9.9375C11.5607 9.9375 11.8125 10.1893 11.8125 10.5V13.5C11.8125 14.432 12.568 15.1875 13.5 15.1875C14.432 15.1875 15.1875 14.432 15.1875 13.5V9.375C15.1875 9.06434 14.9357 8.8125 14.625 8.8125C14.3143 8.8125 14.0625 9.06434 14.0625 9.375V13.5C14.0625 13.8107 13.8107 14.0625 13.5 14.0625C13.1893 14.0625 12.9375 13.8107 12.9375 13.5V9.375C12.9375 8.44302 13.693 7.6875 14.625 7.6875C15.557 7.6875 16.3125 8.44302 16.3125 9.375V13.5C16.3125 15.0533 15.0533 16.3125 13.5 16.3125C11.9467 16.3125 10.6875 15.0533 10.6875 13.5ZM8.625 10.6875C8.93566 10.6875 9.1875 10.9393 9.1875 11.25C9.1875 11.5607 8.93566 11.8125 8.625 11.8125H6C5.68934 11.8125 5.4375 11.5607 5.4375 11.25C5.4375 10.9393 5.68934 10.6875 6 10.6875H8.625ZM9.375 7.6875C9.68566 7.6875 9.9375 7.93934 9.9375 8.25C9.9375 8.56066 9.68566 8.8125 9.375 8.8125H6C5.68934 8.8125 5.4375 8.56066 5.4375 8.25C5.4375 7.93934 5.68934 7.6875 6 7.6875H9.375ZM12 4.6875C12.3107 4.6875 12.5625 4.93934 12.5625 5.25C12.5625 5.56066 12.3107 5.8125 12 5.8125H6C5.68934 5.8125 5.4375 5.56066 5.4375 5.25C5.4375 4.93934 5.68934 4.6875 6 4.6875H12Z" fill="var(--Foreground-fg-quinary--400-, #98A2B3)"/>
    </svg>
  );

  const ClipboardCheckIcon = () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M14.4375 6C14.4375 5.26647 14.4323 5.01002 14.3796 4.81348C14.2236 4.23113 13.7689 3.7764 13.1865 3.62036C13.0515 3.58419 12.8881 3.57039 12.561 3.56543C12.5597 3.66393 12.5568 3.75592 12.5501 3.83862C12.537 3.99841 12.5076 4.17355 12.4197 4.34619C12.2939 4.59302 12.093 4.79387 11.8462 4.91968C11.6736 5.00764 11.4984 5.03698 11.3386 5.05005C11.1854 5.06257 11.0009 5.0625 10.8003 5.0625H7.19971C6.99909 5.0625 6.81459 5.06257 6.66138 5.05005C6.5016 5.03698 6.32645 5.00764 6.15381 4.91968C5.90698 4.79387 5.70614 4.59302 5.58033 4.34619C5.49236 4.17355 5.46303 3.99841 5.44996 3.83862C5.4432 3.75591 5.43955 3.66393 5.43824 3.56543C5.11162 3.5704 4.94845 3.58421 4.81348 3.62036C4.23114 3.7764 3.7764 4.23113 3.62037 4.81348C3.56773 5.01002 3.5625 5.26647 3.5625 6V12.9001C3.5625 13.5394 3.56272 13.9851 3.59107 14.332C3.61888 14.6724 3.67087 14.868 3.74634 15.0161C3.90813 15.3336 4.16637 15.5919 4.48389 15.7537C4.63202 15.8291 4.82761 15.8811 5.16797 15.9089C5.51492 15.9373 5.9606 15.9375 6.59986 15.9375H11.4002C12.0394 15.9375 12.4851 15.9373 12.832 15.9089C13.1724 15.8811 13.368 15.8291 13.5161 15.7537C13.8336 15.5919 14.0919 15.3336 14.2537 15.0161C14.3291 14.868 14.3811 14.6724 14.4089 14.332C14.4373 13.9851 14.4375 13.5394 14.4375 12.9001V6ZM11.2273 8.97729C11.447 8.75763 11.803 8.75763 12.0227 8.97729C12.2424 9.19697 12.2424 9.55303 12.0227 9.77271L8.64771 13.1477C8.42804 13.3674 8.07197 13.3674 7.8523 13.1477L6.3523 11.6477L6.31348 11.6052C6.13325 11.3843 6.14635 11.0582 6.3523 10.8523C6.55825 10.6463 6.88429 10.6332 7.10523 10.8135L7.14771 10.8523L8.25 11.9546L11.2273 8.97729Z" fill="var(--Foreground-fg-quinary--400-, #98A2B3)"/>
    </svg>
  );

  const FileLockIcon = () => (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M15.1875 13.9497C15.1875 13.7306 15.1874 13.5997 15.1794 13.5029C15.1721 13.4129 15.1607 13.4028 15.167 13.415C15.149 13.3798 15.1202 13.351 15.085 13.333C15.0972 13.3393 15.0871 13.3279 14.9971 13.3206C14.9003 13.3126 14.7694 13.3125 14.5503 13.3125H11.6997C11.4806 13.3125 11.3497 13.3126 11.2529 13.3206C11.1629 13.3279 11.1528 13.3393 11.165 13.333C11.1298 13.351 11.101 13.3798 11.083 13.415C11.0893 13.4028 11.0779 13.4129 11.0706 13.5029C11.0626 13.5997 11.0625 13.7306 11.0625 13.9497V14.5503C11.0625 14.7694 11.0626 14.9003 11.0706 14.9971C11.0779 15.0871 11.0893 15.0972 11.083 15.085C11.101 15.1202 11.1298 15.149 11.165 15.167C11.1528 15.1607 11.1629 15.1721 11.2529 15.1794C11.3497 15.1874 11.4806 15.1875 11.6997 15.1875H14.5503C14.7694 15.1875 14.9003 15.1874 14.9971 15.1794C15.0871 15.1721 15.0972 15.1607 15.085 15.167C15.1202 15.149 15.149 15.1202 15.167 15.085C15.1607 15.0972 15.1721 15.0871 15.1794 14.9971C15.1874 14.9003 15.1875 14.7694 15.1875 14.5503V13.9497Z" fill="var(--Foreground-fg-quinary--400-, #98A2B3)"/>
    </svg>
  );

  return (
    <div className={styles.startStep}>
      <div className={styles.container}>
        <div className={styles.checklistCard}>
          <div className={styles.checklistTitle}>Company Incorporation Checklist</div>
          
          <div className={styles.checklistItems}>
            <ChecklistItem
              icon={<FileIcon />}
              title="Company Information"
              description="Provide details about your company structure, activities, and objectives."
            />
            
            <ChecklistItem
              icon={<FileAttachmentIcon />}
              title="Prepare Director, Shareholder & UBO Documents"
              description="Ensure all documents (e.g., passports, proof of address) are ready for submission."
            />
            
            <ChecklistItem
              icon={<ClipboardCheckIcon />}
              title="Complete compliance declaration"
              description="Confirm adherence to Labuan FSA regulations."
            />
            
            <ChecklistItem
              icon={<FileLockIcon />}
              title="Submission & Review Process"
              description="Documents will be submitted to the Labuan FSA after our due diligence."
            />
          </div>

          <div className={styles.noticeSection}>
            <div className={styles.noticeText}>
              BBS will notify you of approval or request additional documents if needed.
            </div>
            <div className={styles.contactText}>
              For urgent inquiries, contact our support team at{' '}
              <a href="mailto:<EMAIL>" className={styles.emailLink}>
                <EMAIL>
              </a>
              .
            </div>
          </div>
        </div>

        <Button 
          type="primary" 
          size="large" 
          className={styles.startButton}
          onClick={onNext}
        >
          Started Now
        </Button>
      </div>
    </div>
  );
};

export default StartStep;
