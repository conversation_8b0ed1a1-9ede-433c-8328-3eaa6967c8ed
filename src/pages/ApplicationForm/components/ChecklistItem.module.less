// ChecklistItem 组件样式
.checklistItem {
  display: flex;
  align-items: flex-start;
  gap: 6px;
  width: 100%;

  .icon {
    position: relative;
    flex-shrink: 0;
    width: 18px;
    height: 18px;
    margin-top: 2px;

    svg {
      width: 100%;
      height: 100%;
    }
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    gap: 2px;

    .title {
      width: 100%;
      color: var(--Text-text-secondary-(700), #344054);
      font-size: 14px;
      font-family: 'Inter', sans-serif;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: 0.14px;
      word-wrap: break-word;
    }

    .description {
      width: 100%;
      color: var(--Text-text-tertiary-(600), #475467);
      font-size: 12px;
      font-family: 'Inter', sans-serif;
      font-weight: 400;
      line-height: 18px;
      letter-spacing: 0.12px;
      word-wrap: break-word;
    }
  }
}
