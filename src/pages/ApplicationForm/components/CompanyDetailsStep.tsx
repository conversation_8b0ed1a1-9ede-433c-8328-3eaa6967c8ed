import React, { useState } from "react";
import { Button, Input, Select, Radio, Form, Row, Col } from "antd";
import type { CompanyDetailsFormData } from "@/types/applicationForm";
import styles from "./CompanyDetailsStep.module.less";

const { TextArea } = Input;
const { Option } = Select;

interface CompanyDetailsStepProps {
  onNext: (data: CompanyDetailsFormData) => void;
  onBack: () => void;
  initialData?: CompanyDetailsFormData;
}

const CompanyDetailsStep: React.FC<CompanyDetailsStepProps> = ({
  onNext,
  onBack,
  initialData,
}) => {
  const [form] = Form.useForm();
  const [formData, setFormData] = useState<CompanyDetailsFormData>(
    initialData || {
      numberOfShares: "",
      denominatedCurrency: "USD",
      issuePricePerShare: "",
      sourceOfInitialFunding: "",
      businessActivities: "",
      labuanLicenseTrading: "",
      labuanNonLicenseTrading: "",
      labuanNonTrading: "",
      activitiesOtherThanLabuan: "",
      businessDescription: "",
      requireWorkingPermit: undefined,
      numberOfWorkingPermit: "",
      numberOfDependentPass: "",
    }
  );

  const handleFormChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = () => {
    form
      .validateFields()
      .then(() => {
        onNext(formData);
      })
      .catch(() => {
        // 表单验证失败
      });
  };

  const currencyOptions = [
    { value: "USD", label: "USD" },
    { value: "MYR", label: "MYR" },
    { value: "SGD", label: "SGD" },
    { value: "EUR", label: "EUR" },
    { value: "GBP", label: "GBP" },
  ];

  const fundingSourceOptions = [
    { value: "personal_savings", label: "Personal Savings" },
    { value: "business_income", label: "Business Income" },
    { value: "investment", label: "Investment" },
    { value: "loan", label: "Loan" },
    { value: "other", label: "Other" },
  ];

  const businessActivityOptions = [
    { value: "trading", label: "Trading" },
    { value: "investment_holding", label: "Investment Holding" },
    { value: "consulting", label: "Consulting" },
    { value: "manufacturing", label: "Manufacturing" },
    { value: "other", label: "Other" },
  ];

  return (
    <div className={styles.companyDetailsStep}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Company Details</h1>
        </div>

        <div className={styles.formCard}>
          <Form
            form={form}
            layout="vertical"
            initialValues={formData}
            className={styles.form}
          >
            <Form.Item
              name="numberOfShares"
              label="Number of Shares"
              rules={[
                { required: true, message: "Please enter number of shares" },
              ]}
            >
              <Input
                placeholder="Enter Number of Shares"
                value={formData.numberOfShares}
                onChange={(e) =>
                  handleFormChange("numberOfShares", e.target.value)
                }
                className={styles.inputField}
              />
            </Form.Item>

            <Row gutter={32}>
              <Col span={10}>
                <Form.Item
                  name="denominatedCurrency"
                  label="Denominated Currency"
                  rules={[
                    { required: true, message: "Please select currency" },
                  ]}
                >
                  <Select
                    value={formData.denominatedCurrency}
                    onChange={(value) =>
                      handleFormChange("denominatedCurrency", value)
                    }
                    className={styles.selectField}
                  >
                    {currencyOptions.map((option) => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={14}>
                <Form.Item
                  name="issuePricePerShare"
                  label="Issue Price Per Share"
                  rules={[
                    {
                      required: true,
                      message: "Please enter issue price per share",
                    },
                  ]}
                >
                  <Input
                    placeholder="Enter Price"
                    value={formData.issuePricePerShare}
                    onChange={(e) =>
                      handleFormChange("issuePricePerShare", e.target.value)
                    }
                    className={styles.inputField}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="sourceOfInitialFunding"
              label="Kindly elaborate the source of initial funding of the Labuan company."
              rules={[
                {
                  required: true,
                  message: "Please select source of initial funding",
                },
              ]}
            >
              <Select
                placeholder="Select Source of Initial Fund"
                value={formData.sourceOfInitialFunding}
                onChange={(value) =>
                  handleFormChange("sourceOfInitialFunding", value)
                }
                className={styles.selectField}
              >
                {fundingSourceOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="businessActivities"
              label="Choose Relevant Business Activities"
              rules={[
                {
                  required: true,
                  message: "Please select business activities",
                },
              ]}
            >
              <Select
                placeholder="Select here"
                value={formData.businessActivities}
                onChange={(value) =>
                  handleFormChange("businessActivities", value)
                }
                className={styles.selectField}
              >
                {businessActivityOptions.map((option) => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="labuanLicenseTrading"
              label="Labuan License-Trading Activity"
            >
              <Select
                placeholder="Select here"
                value={formData.labuanLicenseTrading}
                onChange={(value) =>
                  handleFormChange("labuanLicenseTrading", value)
                }
                className={styles.selectField}
              >
                <Option value="banking">Banking</Option>
                <Option value="insurance">Insurance</Option>
                <Option value="fund_management">Fund Management</Option>
                <Option value="other">Other</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="labuanNonLicenseTrading"
              label="Labuan Non-Licensed Trading Activities"
            >
              <Select
                placeholder="Select here"
                value={formData.labuanNonLicenseTrading}
                onChange={(value) =>
                  handleFormChange("labuanNonLicenseTrading", value)
                }
                className={styles.selectField}
              >
                <Option value="trading">Trading</Option>
                <Option value="consulting">Consulting</Option>
                <Option value="services">Services</Option>
                <Option value="other">Other</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="labuanNonTrading"
              label="Labuan Non-Trading Activity"
            >
              <Select
                placeholder="Select here"
                value={formData.labuanNonTrading}
                onChange={(value) =>
                  handleFormChange("labuanNonTrading", value)
                }
                className={styles.selectField}
              >
                <Option value="holding">Holding Company</Option>
                <Option value="investment">Investment</Option>
                <Option value="other">Other</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="activitiesOtherThanLabuan"
              label="Activities other than Labuan Business"
            >
              <Select
                placeholder="Select here"
                value={formData.activitiesOtherThanLabuan}
                onChange={(value) =>
                  handleFormChange("activitiesOtherThanLabuan", value)
                }
                className={styles.selectField}
              >
                <Option value="domestic_business">Domestic Business</Option>
                <Option value="international_business">
                  International Business
                </Option>
                <Option value="other">Other</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="businessDescription"
              label="Describe Nature of Business in Details"
              rules={[
                {
                  required: true,
                  message: "Please describe the nature of business",
                },
              ]}
            >
              <TextArea
                placeholder="Enter Business Description"
                value={formData.businessDescription}
                onChange={(e) =>
                  handleFormChange("businessDescription", e.target.value)
                }
                className={styles.textareaField}
                rows={6}
              />
            </Form.Item>

            <Form.Item
              name="requireWorkingPermit"
              label="Do you require working permit for the Labuan company?"
              rules={[{ required: true, message: "Please select an option" }]}
            >
              <Radio.Group
                value={formData.requireWorkingPermit}
                onChange={(e) =>
                  handleFormChange("requireWorkingPermit", e.target.value)
                }
                className={styles.radioGroup}
              >
                <Radio value={true}>Yes</Radio>
                <Radio value={false}>No</Radio>
              </Radio.Group>
            </Form.Item>

            {formData.requireWorkingPermit && (
              <Row gutter={32}>
                <Col span={12}>
                  <Form.Item
                    name="numberOfWorkingPermit"
                    label="Number of Application of Working Permit"
                    rules={[
                      {
                        required: true,
                        message: "Please enter number of working permits",
                      },
                    ]}
                  >
                    <Input
                      placeholder="1"
                      value={formData.numberOfWorkingPermit}
                      onChange={(e) =>
                        handleFormChange(
                          "numberOfWorkingPermit",
                          e.target.value
                        )
                      }
                      className={styles.inputField}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="numberOfDependentPass"
                    label="Number of Application of Dependent Pass"
                    rules={[
                      {
                        required: true,
                        message: "Please enter number of dependent passes",
                      },
                    ]}
                  >
                    <Input
                      placeholder="1"
                      value={formData.numberOfDependentPass}
                      onChange={(e) =>
                        handleFormChange(
                          "numberOfDependentPass",
                          e.target.value
                        )
                      }
                      className={styles.inputField}
                    />
                  </Form.Item>
                </Col>
              </Row>
            )}
          </Form>
        </div>

        <div className={styles.buttonGroup}>
          <Button
            type="default"
            size="large"
            onClick={onBack}
            className={styles.backButton}
          >
            Back
          </Button>
          <Button
            type="primary"
            size="large"
            onClick={handleSubmit}
            className={styles.nextButton}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CompanyDetailsStep;
