import React, { useState } from "react";
import {
  Form,
  Input,
  Select,
  Button,
  Checkbox,
  Radio,
  Space,
  Upload,
} from "antd";
import { ArrowLeftOutlined, InboxOutlined } from "@ant-design/icons";
import type { CorporateShareholderFormData } from "@/types/applicationForm";
import styles from "./ShareholderDetailStep.module.less";

const { Dragger } = Upload;

interface CorporateShareholderStepProps {
  onNext: (data: CorporateShareholderFormData) => void;
  onBack: () => void;
  initialData?: CorporateShareholderFormData;
  shareholderName?: string;
}

const CorporateShareholderStep: React.FC<CorporateShareholderStepProps> = ({
  onNext,
  onBack,
  initialData,
  shareholderName = "ABC Company",
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      const formData: CorporateShareholderFormData = {
        corporateName: values.corporateName,
        entityNo: values.entityNo,
        incorporationDate: values.incorporationDate,
        address: values.address,
        phoneNumber: values.phoneNumber,
        email: values.email,
        shareholdingPercentage: values.shareholdingPercentage,
        authorizedRepresentative: {
          firstName: values.repFirstName,
          lastName: values.repLastName,
          nricPassport: values.repNricPassport,
          dateOfBirth: values.repDateOfBirth,
          nationality: values.repNationality,
          position: values.repPosition,
        },
      };
      onNext(formData);
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.shareholderDetailStep}>
      <div className={styles.container}>
        <div className={styles.header}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
            className={styles.backButton}
          >
            Back to List
          </Button>
          <h1 className={styles.title}>{shareholderName} - KYC Details</h1>
        </div>

        <div className={styles.formContainer}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={initialData}
            className={styles.form}
          >
            {/* 根据 Figma 设计重构 - 完整表单 */}

            {/* 第一部分：UBO 问题和文档上传 */}
            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 32, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
                <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
                  <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#081021', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Does this shareholder have an ultimate beneficial owner?</div>
                </div>
                <Form.Item name="hasUBO" rules={[{ required: true, message: "Please select an option" }]} style={{ marginBottom: 0 }}>
                  <Radio.Group>
                    <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
                      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
                        <Radio value={true}>
                          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#667085', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
                        </Radio>
                      </div>
                      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
                        <Radio value={false}>
                          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#667085', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
                        </Radio>
                      </div>
                    </div>
                  </Radio.Group>
                </Form.Item>
              </div>

              <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
                <div style={{alignSelf: 'stretch', color: '#081021', fontSize: 18, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.18, wordWrap: 'break-word'}}>Kindly upload the documents of the ultimate beneficial owner.</div>
                <div style={{alignSelf: 'stretch', padding: 12, background: '#E7EFFF', borderRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                  <div style={{alignSelf: 'stretch', color: '#204184', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>For Individual Directors and Shareholders:</div>
                  <div style={{alignSelf: 'stretch', color: '#204184', fontSize: 12, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>Malaysian: Upload a copy of Malaysian identity card (MyKad).</div>
                  <div style={{alignSelf: 'stretch'}}>
                    <span style={{color: '#204184', fontSize: 12, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>Foreigners</span>
                    <span style={{color: '#204184', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>: Upload a valid passport with a clear photo and signature.</span>
                  </div>
                </div>
                <div style={{alignSelf: 'stretch', padding: 12, background: '#E7EFFF', borderRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
                  <div style={{alignSelf: 'stretch', color: '#204184', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>For Corporate Directors & Shareholders:</div>
                  <div style={{alignSelf: 'stretch', color: '#204184', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>Upload a certified copy of the company's Certificate of Incorporation.</div>
                </div>
              </div>

              <Form.Item name="initialDocuments" style={{ marginBottom: 0, alignSelf: 'stretch' }}>
                <Dragger multiple accept=".png,.jpeg,.jpg,.pdf">
                  <div style={{alignSelf: 'stretch', height: 200, paddingLeft: 24, paddingRight: 24, paddingTop: 16, paddingBottom: 16, background: 'white', borderRadius: 12, border: '1.50px solid #D0D5DD', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
                      <div style={{width: 40, height: 40, position: 'relative', background: 'white', overflow: 'hidden', borderRadius: 8, border: '1px solid #D0D5DD'}}>
                        <div style={{width: 20, height: 20, left: 10, top: 10, position: 'absolute', overflow: 'hidden'}}>
                          <InboxOutlined style={{fontSize: 20, color: '#CECFD2'}} />
                        </div>
                      </div>
                      <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
                        <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 4, display: 'inline-flex'}}>
                          <div style={{padding: 2, borderRadius: 10, justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                            <div style={{color: '#344054', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Click to upload</div>
                          </div>
                          <div style={{color: '#475467', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>or drag and drop</div>
                        </div>
                        <div style={{alignSelf: 'stretch', textAlign: 'center', color: '#475467', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>PNG, JPEG, PDF (max size file limit 5MB)</div>
                      </div>
                    </div>
                  </div>
                </Dragger>
              </Form.Item>
            </div>
              <div
                style={{
                  alignSelf: "stretch",
                  flexDirection: "column",
                  justifyContent: "flex-start",
                  alignItems: "flex-start",
                  gap: 12,
                  display: "flex",
                }}
              >
                <div
                  style={{
                    alignSelf: "stretch",
                    justifyContent: "flex-start",
                    alignItems: "flex-end",
                    gap: 4,
                    display: "inline-flex",
                  }}
                >
                  <div
                    style={{
                      flex: "1 1 0",
                      justifyContent: "center",
                      display: "flex",
                      flexDirection: "column",
                      color: "#081021",
                      fontSize: 14,
                      fontFamily: "Inter",
                      fontWeight: "500",
                      lineHeight: 18,
                      letterSpacing: 0.14,
                      wordWrap: "break-word",
                    }}
                  >
                    Does this shareholder have an ultimate beneficial owner?
                  </div>
                </div>
                <Form.Item
                  name="hasUBO"
                  rules={[
                    { required: true, message: "Please select an option" },
                  ]}
                  style={{ marginBottom: 0 }}
                >
                  <Radio.Group>
                    <div
                      style={{
                        justifyContent: "flex-start",
                        alignItems: "flex-start",
                        gap: 40,
                        display: "inline-flex",
                      }}
                    >
                      <div
                        style={{
                          justifyContent: "flex-start",
                          alignItems: "center",
                          gap: 12,
                          display: "flex",
                        }}
                      >
                        <Radio value={true}>
                          <div
                            style={{
                              justifyContent: "center",
                              display: "flex",
                              flexDirection: "column",
                              color: "#667085",
                              fontSize: 16,
                              fontFamily: "Inter",
                              fontWeight: "400",
                              lineHeight: 20,
                              letterSpacing: 0.16,
                              wordWrap: "break-word",
                            }}
                          >
                            Yes
                          </div>
                        </Radio>
                      </div>
                      <div
                        style={{
                          justifyContent: "flex-start",
                          alignItems: "center",
                          gap: 12,
                          display: "flex",
                        }}
                      >
                        <Radio value={false}>
                          <div
                            style={{
                              justifyContent: "center",
                              display: "flex",
                              flexDirection: "column",
                              color: "#667085",
                              fontSize: 16,
                              fontFamily: "Inter",
                              fontWeight: "400",
                              lineHeight: 20,
                              letterSpacing: 0.16,
                              wordWrap: "break-word",
                            }}
                          >
                            No
                          </div>
                        </Radio>
                      </div>
                    </div>
                  </Radio.Group>
                </Form.Item>
              </div>

              <div
                style={{
                  alignSelf: "stretch",
                  flexDirection: "column",
                  justifyContent: "flex-start",
                  alignItems: "flex-start",
                  gap: 16,
                  display: "flex",
                }}
              >
                <div
                  style={{
                    alignSelf: "stretch",
                    color: "#081021",
                    fontSize: 18,
                    fontFamily: "Inter",
                    fontWeight: "500",
                    lineHeight: 24,
                    letterSpacing: 0.18,
                    wordWrap: "break-word",
                  }}
                >
                  Kindly upload the documents of the ultimate beneficial owner.
                </div>
                <div
                  style={{
                    alignSelf: "stretch",
                    padding: 12,
                    background: "#E7EFFF",
                    borderRadius: 8,
                    flexDirection: "column",
                    justifyContent: "flex-start",
                    alignItems: "flex-start",
                    gap: 4,
                    display: "flex",
                  }}
                >
                  <div
                    style={{
                      alignSelf: "stretch",
                      color: "#204184",
                      fontSize: 14,
                      fontFamily: "Inter",
                      fontWeight: "600",
                      lineHeight: 18,
                      letterSpacing: 0.14,
                      wordWrap: "break-word",
                    }}
                  >
                    For Individual Directors and Shareholders:
                  </div>
                  <div
                    style={{
                      alignSelf: "stretch",
                      color: "#204184",
                      fontSize: 12,
                      fontFamily: "Inter",
                      fontWeight: "500",
                      lineHeight: 18,
                      letterSpacing: 0.12,
                      wordWrap: "break-word",
                    }}
                  >
                    Malaysian: Upload a copy of Malaysian identity card (MyKad).
                  </div>
                  <div style={{ alignSelf: "stretch" }}>
                    <span
                      style={{
                        color: "#204184",
                        fontSize: 12,
                        fontFamily: "Inter",
                        fontWeight: "500",
                        lineHeight: 18,
                        letterSpacing: 0.12,
                        wordWrap: "break-word",
                      }}
                    >
                      Foreigners
                    </span>
                    <span
                      style={{
                        color: "#204184",
                        fontSize: 12,
                        fontFamily: "Inter",
                        fontWeight: "400",
                        lineHeight: 18,
                        letterSpacing: 0.12,
                        wordWrap: "break-word",
                      }}
                    >
                      : Upload a valid passport with a clear photo and
                      signature.
                    </span>
                  </div>
                </div>
                <div
                  style={{
                    alignSelf: "stretch",
                    padding: 12,
                    background: "#E7EFFF",
                    borderRadius: 8,
                    flexDirection: "column",
                    justifyContent: "flex-start",
                    alignItems: "flex-start",
                    gap: 4,
                    display: "flex",
                  }}
                >
                  <div
                    style={{
                      alignSelf: "stretch",
                      color: "#204184",
                      fontSize: 14,
                      fontFamily: "Inter",
                      fontWeight: "600",
                      lineHeight: 18,
                      letterSpacing: 0.14,
                      wordWrap: "break-word",
                    }}
                  >
                    For Corporate Directors & Shareholders:
                  </div>
                  <div
                    style={{
                      alignSelf: "stretch",
                      color: "#204184",
                      fontSize: 12,
                      fontFamily: "Inter",
                      fontWeight: "400",
                      lineHeight: 18,
                      letterSpacing: 0.12,
                      wordWrap: "break-word",
                    }}
                  >
                    Upload a certified copy of the company's Certificate of
                    Incorporation.
                  </div>
                </div>
              </div>

              <Form.Item
                name="initialDocuments"
                style={{ marginBottom: 0, alignSelf: "stretch" }}
              >
                <div
                  style={{
                    alignSelf: "stretch",
                    height: 200,
                    paddingLeft: 24,
                    paddingRight: 24,
                    paddingTop: 16,
                    paddingBottom: 16,
                    background: "white",
                    borderRadius: 12,
                    border: "1.50px solid #D0D5DD",
                    flexDirection: "column",
                    justifyContent: "center",
                    alignItems: "center",
                    gap: 4,
                    display: "flex",
                  }}
                >
                  <div
                    style={{
                      alignSelf: "stretch",
                      flexDirection: "column",
                      justifyContent: "flex-start",
                      alignItems: "center",
                      gap: 12,
                      display: "flex",
                    }}
                  >
                    <div
                      style={{
                        width: 40,
                        height: 40,
                        position: "relative",
                        background: "white",
                        overflow: "hidden",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                      }}
                    >
                      <div
                        style={{
                          width: 20,
                          height: 20,
                          left: 10,
                          top: 10,
                          position: "absolute",
                          overflow: "hidden",
                        }}
                      >
                        <InboxOutlined
                          style={{ fontSize: 20, color: "#CECFD2" }}
                        />
                      </div>
                    </div>
                    <div
                      style={{
                        alignSelf: "stretch",
                        flexDirection: "column",
                        justifyContent: "flex-start",
                        alignItems: "center",
                        gap: 4,
                        display: "flex",
                      }}
                    >
                      <div
                        style={{
                          alignSelf: "stretch",
                          justifyContent: "center",
                          alignItems: "flex-start",
                          gap: 4,
                          display: "inline-flex",
                        }}
                      >
                        <div
                          style={{
                            padding: 2,
                            borderRadius: 10,
                            justifyContent: "center",
                            alignItems: "center",
                            gap: 4,
                            display: "flex",
                          }}
                        >
                          <div
                            style={{
                              color: "#344054",
                              fontSize: 14,
                              fontFamily: "Inter",
                              fontWeight: "600",
                              lineHeight: 18,
                              letterSpacing: 0.14,
                              wordWrap: "break-word",
                            }}
                          >
                            Click to upload
                          </div>
                        </div>
                        <div
                          style={{
                            color: "#475467",
                            fontSize: 14,
                            fontFamily: "Inter",
                            fontWeight: "400",
                            lineHeight: 18,
                            letterSpacing: 0.14,
                            wordWrap: "break-word",
                          }}
                        >
                          or drag and drop
                        </div>
                      </div>
                      <div
                        style={{
                          alignSelf: "stretch",
                          textAlign: "center",
                          color: "#475467",
                          fontSize: 12,
                          fontFamily: "Inter",
                          fontWeight: "400",
                          lineHeight: 18,
                          letterSpacing: 0.12,
                          wordWrap: "break-word",
                        }}
                      >
                        PNG, JPEG, PDF (max size file limit 5MB)
                      </div>
                    </div>
                  </div>
                </div>
              </Form.Item>
            </div>

            {/* 第二部分：基本信息表单 - 根据 Figma 1.txt */}
            <div style={{width: 804, padding: 24, background: 'white', borderRadius: 12, border: '1px solid #D0D5DD', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'inline-flex'}}>
              {/* Role in the Company */}
              <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
                <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
                  <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#081021', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Role in the Company</div>
                </div>
                <Form.Item name="roles" style={{ marginBottom: 0 }}>
                  <Checkbox.Group>
                    <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'inline-flex', flexWrap: 'wrap'}}>
                      <div style={{minWidth: 189, padding: '20px 16px', background: 'white', borderRadius: 8, border: '1px solid #D0D5DD', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'inline-flex'}}>
                        <Checkbox value="director">
                          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#081021', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Director</div>
                        </Checkbox>
                      </div>
                      <div style={{minWidth: 189, padding: '20px 16px', background: 'white', borderRadius: 8, border: '1px solid #D0D5DD', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'inline-flex'}}>
                        <Checkbox value="shareholder">
                          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#081021', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Shareholder</div>
                        </Checkbox>
                      </div>
                      <div style={{minWidth: 189, padding: '20px 16px', background: 'white', borderRadius: 8, border: '1px solid #D0D5DD', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'inline-flex'}}>
                        <Checkbox value="ultimate_beneficial_owners">
                          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#081021', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Ultimate Beneficial Owners</div>
                        </Checkbox>
                      </div>
                    </div>
                  </Checkbox.Group>
                </Form.Item>
              </div>

              {/* Share Information */}
              <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
                <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
                  <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
                    <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#081021', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Number & Percentage of share(s) to be issued</div>
                  </div>
                  <Form.Item name="sharePercentage" rules={[{ required: true, message: "Please enter percentage" }]} style={{ marginBottom: 0 }}>
                    <Input
                      placeholder="%"
                      style={{padding: 12, background: 'white', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', borderRadius: 8, border: '1px solid #D0D5DD', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, color: '#667085'}}
                    />
                  </Form.Item>
                </div>
                <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
                  <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
                    <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#081021', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>UBO Percentage of shares</div>
                  </div>
                  <Form.Item name="uboPercentage" rules={[{ required: true, message: "Please enter UBO percentage" }]} style={{ marginBottom: 0 }}>
                    <Input
                      placeholder="%"
                      style={{padding: 12, background: 'white', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', borderRadius: 8, border: '1px solid #D0D5DD', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, color: '#667085'}}
                    />
                  </Form.Item>
                </div>
              </div>
            </div>

            {/* 第三部分：更多基本信息 - 根据 Figma 2.txt */}
            <div style={{width: 804, padding: 24, background: 'white', borderRadius: 12, border: '1px solid #D0D5DD', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'inline-flex'}}>
              {/* Jurisdiction */}
              <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
                <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
                  <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#081021', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Jurisdiction</div>
                </div>
                <Form.Item name="jurisdiction" style={{ marginBottom: 0 }}>
                  <Input
                    placeholder="Enter Jurisdiction"
                    style={{padding: 12, background: 'white', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', borderRadius: 8, border: '1px solid #D0D5DD', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, color: '#667085'}}
                  />
                </Form.Item>
              </div>
            </div>

            {/* 第四部分：财务和税务信息 - 根据 Figma 3.txt */}
            <div style={{width: 804, padding: 24, background: 'white', borderRadius: 12, border: '1px solid #D0D5DD', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'inline-flex'}}>
              {/* Tax Information */}
              <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
                <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
                  <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
                    <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#081021', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Tax Residency</div>
                  </div>
                  <Form.Item name="taxResidency" style={{ marginBottom: 0 }}>
                    <Input
                      placeholder="Enter Tax residency"
                      style={{padding: 12, background: 'white', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', borderRadius: 8, border: '1px solid #D0D5DD', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, color: '#667085'}}
                    />
                  </Form.Item>
                </div>
                <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
                  <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
                    <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: '#081021', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>TIN</div>
                  </div>
                  <Form.Item name="tin" style={{ marginBottom: 0 }}>
                    <Input
                      placeholder="Enter Tax No"
                      style={{padding: 12, background: 'white', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', borderRadius: 8, border: '1px solid #D0D5DD', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, color: '#667085'}}
                    />
                  </Form.Item>
                </div>
              </div>
            </div>
                          </span>
                        </Checkbox>
                      </div>
                      <div
                        style={{
                          minWidth: 189,
                          padding: "20px 16px",
                          background: "white",
                          borderRadius: 8,
                          border: "1px solid #D0D5DD",
                          display: "flex",
                          alignItems: "center",
                          gap: 12,
                        }}
                      >
                        <Checkbox value="shareholder">
                          <span
                            style={{
                              color: "#081021",
                              fontSize: 16,
                              fontFamily: "Inter",
                              fontWeight: 400,
                              lineHeight: "20px",
                              letterSpacing: "0.16px",
                            }}
                          >
                            Shareholder
                          </span>
                        </Checkbox>
                      </div>
                      <div
                        style={{
                          minWidth: 189,
                          padding: "20px 16px",
                          background: "white",
                          borderRadius: 8,
                          border: "1px solid #D0D5DD",
                          display: "flex",
                          alignItems: "center",
                          gap: 12,
                        }}
                      >
                        <Checkbox value="ultimate_beneficial_owners">
                          <span
                            style={{
                              color: "#081021",
                              fontSize: 16,
                              fontFamily: "Inter",
                              fontWeight: 400,
                              lineHeight: "20px",
                              letterSpacing: "0.16px",
                            }}
                          >
                            Ultimate Beneficial Owners
                          </span>
                        </Checkbox>
                      </div>
                    </div>
                  </Checkbox.Group>
                </Form.Item>
              </div>

              {/* Share Information */}
              <div style={{ display: "flex", gap: 40 }}>
                <div style={{ flex: "1 1 0" }}>
                  <Form.Item
                    label="Number & Percentage of share(s) to be issued"
                    name="sharePercentage"
                    rules={[
                      { required: true, message: "Please enter percentage" },
                    ]}
                    style={{ marginBottom: 0 }}
                  >
                    <Input
                      placeholder="%"
                      style={{
                        padding: 12,
                        background: "white",
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        fontSize: 16,
                        fontFamily: "Inter",
                        fontWeight: 400,
                        lineHeight: "20px",
                        letterSpacing: "0.16px",
                        color: "#667085",
                      }}
                    />
                  </Form.Item>
                </div>
                <div style={{ flex: "1 1 0" }}>
                  <Form.Item
                    label="UBO Percentage of shares"
                    name="uboPercentage"
                    rules={[
                      {
                        required: true,
                        message: "Please enter UBO percentage",
                      },
                    ]}
                    style={{ marginBottom: 0 }}
                  >
                    <Input
                      placeholder="%"
                      style={{
                        padding: 12,
                        background: "white",
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        fontSize: 16,
                        fontFamily: "Inter",
                        fontWeight: 400,
                        lineHeight: "20px",
                        letterSpacing: "0.16px",
                        color: "#667085",
                      }}
                    />
                  </Form.Item>
                </div>
              </div>

              {/* Jurisdiction */}
              <div style={{ display: "flex", flexDirection: "column", gap: 6 }}>
                <div
                  style={{
                    color: "#081021",
                    fontSize: 14,
                    fontFamily: "Inter",
                    fontWeight: 500,
                    lineHeight: "18px",
                    letterSpacing: "0.14px",
                  }}
                >
                  Jurisdiction
                </div>
                <Form.Item name="jurisdiction" style={{ marginBottom: 0 }}>
                  <Input
                    placeholder="Enter Jurisdiction"
                    style={{
                      padding: 12,
                      background: "white",
                      boxShadow:
                        "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      borderRadius: 8,
                      border: "1px solid #D0D5DD",
                      fontSize: 16,
                      fontFamily: "Inter",
                      fontWeight: 400,
                      lineHeight: "20px",
                      letterSpacing: "0.16px",
                      color: "#667085",
                    }}
                  />
                </Form.Item>
              </div>

              {/* Nature of Business */}
              <div style={{ display: "flex", flexDirection: "column", gap: 6 }}>
                <div
                  style={{
                    color: "#081021",
                    fontSize: 14,
                    fontFamily: "Inter",
                    fontWeight: 500,
                    lineHeight: "18px",
                    letterSpacing: "0.14px",
                  }}
                >
                  Nature of Business
                </div>
                <Form.Item name="natureOfBusiness" style={{ marginBottom: 0 }}>
                  <Input
                    placeholder="Enter Nature of Business"
                    style={{
                      padding: 12,
                      background: "white",
                      boxShadow:
                        "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      borderRadius: 8,
                      border: "1px solid #D0D5DD",
                      fontSize: 16,
                      fontFamily: "Inter",
                      fontWeight: 400,
                      lineHeight: "20px",
                      letterSpacing: "0.16px",
                      color: "#667085",
                    }}
                  />
                </Form.Item>
              </div>

              {/* Phone Numbers */}
              <div style={{ display: "flex", gap: 40 }}>
                <div style={{ flex: "1 1 0" }}>
                  <Form.Item
                    label="Telephone Number"
                    name="telephoneNumber"
                    style={{ marginBottom: 0 }}
                  >
                    <Input
                      placeholder="Enter Telephone Number"
                      style={{
                        padding: 12,
                        background: "white",
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        fontSize: 16,
                        fontFamily: "Inter",
                        fontWeight: 400,
                        lineHeight: "20px",
                        letterSpacing: "0.16px",
                        color: "#667085",
                      }}
                    />
                  </Form.Item>
                </div>
                <div style={{ flex: "1 1 0" }}>
                  <Form.Item
                    label="Mobile Number"
                    name="mobileNumber"
                    style={{ marginBottom: 0 }}
                  >
                    <Input
                      placeholder="Enter Mobile Number"
                      style={{
                        padding: 12,
                        background: "white",
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        fontSize: 16,
                        fontFamily: "Inter",
                        fontWeight: 400,
                        lineHeight: "20px",
                        letterSpacing: "0.16px",
                        color: "#667085",
                      }}
                    />
                  </Form.Item>
                </div>
              </div>

              {/* Email */}
              <div style={{ display: "flex", gap: 32 }}>
                <div style={{ flex: "1 1 0" }}>
                  <Form.Item
                    label="Email"
                    name="email"
                    style={{ marginBottom: 0 }}
                  >
                    <Input
                      placeholder="Enter Email"
                      style={{
                        padding: 12,
                        background: "white",
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        fontSize: 16,
                        fontFamily: "Inter",
                        fontWeight: 400,
                        lineHeight: "20px",
                        letterSpacing: "0.16px",
                        color: "#667085",
                      }}
                    />
                  </Form.Item>
                </div>
              </div>
            </div>

            {/* 继续添加更多表单字段 */}
            <div
              style={{
                width: 804,
                padding: 24,
                background: "white",
                borderRadius: 12,
                border: "1px solid #E4E7EC",
                marginBottom: 32,
                display: "flex",
                flexDirection: "column",
                gap: 32,
              }}
            >
              {/* Tax Information */}
              <div style={{ display: "flex", gap: 40 }}>
                <div style={{ flex: "1 1 0" }}>
                  <Form.Item
                    label="Tax Residency"
                    name="taxResidency"
                    style={{ marginBottom: 0 }}
                  >
                    <Input
                      placeholder="Enter Tax residency"
                      style={{
                        padding: 12,
                        background: "white",
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        fontSize: 16,
                        fontFamily: "Inter",
                        fontWeight: 400,
                        lineHeight: "20px",
                        letterSpacing: "0.16px",
                        color: "#667085",
                      }}
                    />
                  </Form.Item>
                </div>
                <div style={{ flex: "1 1 0" }}>
                  <Form.Item label="TIN" name="tin" style={{ marginBottom: 0 }}>
                    <Input
                      placeholder="Enter Tax No"
                      style={{
                        padding: 12,
                        background: "white",
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        fontSize: 16,
                        fontFamily: "Inter",
                        fontWeight: 400,
                        lineHeight: "20px",
                        letterSpacing: "0.16px",
                        color: "#667085",
                      }}
                    />
                  </Form.Item>
                </div>
              </div>

              {/* Financial Information */}
              <div style={{ display: "flex", gap: 40 }}>
                <div style={{ flex: "1 1 0" }}>
                  <Form.Item
                    label="Estimated Net Worth"
                    name="estimatedNetWorth"
                    style={{ marginBottom: 0 }}
                  >
                    <Select
                      placeholder="Select"
                      style={{
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      }}
                      suffixIcon={
                        <div
                          style={{
                            width: 16,
                            height: 16,
                            position: "relative",
                          }}
                        >
                          <div
                            style={{
                              width: 11.56,
                              height: 5.73,
                              left: 2.22,
                              top: 5.47,
                              position: "absolute",
                              background: "#475467",
                            }}
                          />
                        </div>
                      }
                    >
                      <Select.Option value="below_100k">
                        Below $100,000
                      </Select.Option>
                      <Select.Option value="100k_500k">
                        $100,000 - $500,000
                      </Select.Option>
                      <Select.Option value="500k_1m">
                        $500,000 - $1,000,000
                      </Select.Option>
                      <Select.Option value="1m_5m">
                        $1,000,000 - $5,000,000
                      </Select.Option>
                      <Select.Option value="above_5m">
                        Above $5,000,000
                      </Select.Option>
                    </Select>
                  </Form.Item>
                </div>
                <div style={{ flex: "1 1 0" }}>
                  <Form.Item
                    label="Annual Income/Annual Revenue"
                    name="annualIncome"
                    style={{ marginBottom: 0 }}
                  >
                    <Select
                      placeholder="Select"
                      style={{
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      }}
                      suffixIcon={
                        <div
                          style={{
                            width: 16,
                            height: 16,
                            position: "relative",
                          }}
                        >
                          <div
                            style={{
                              width: 11.56,
                              height: 5.73,
                              left: 2.22,
                              top: 5.47,
                              position: "absolute",
                              background: "#475467",
                            }}
                          />
                        </div>
                      }
                    >
                      <Select.Option value="below_50k">
                        Below $50,000
                      </Select.Option>
                      <Select.Option value="50k_100k">
                        $50,000 - $100,000
                      </Select.Option>
                      <Select.Option value="100k_500k">
                        $100,000 - $500,000
                      </Select.Option>
                      <Select.Option value="500k_1m">
                        $500,000 - $1,000,000
                      </Select.Option>
                      <Select.Option value="above_1m">
                        Above $1,000,000
                      </Select.Option>
                    </Select>
                  </Form.Item>
                </div>
              </div>

              {/* Source of Wealth */}
              <div style={{ display: "flex", gap: 40 }}>
                <div style={{ width: 358 }}>
                  <Form.Item
                    label="Source of Wealth"
                    name="sourceOfWealth"
                    style={{ marginBottom: 0 }}
                  >
                    <Select
                      placeholder="Select"
                      style={{
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      }}
                      suffixIcon={
                        <div
                          style={{
                            width: 16,
                            height: 16,
                            position: "relative",
                          }}
                        >
                          <div
                            style={{
                              width: 11.56,
                              height: 5.73,
                              left: 2.22,
                              top: 5.47,
                              position: "absolute",
                              background: "#475467",
                            }}
                          />
                        </div>
                      }
                    >
                      <Select.Option value="employment">
                        Employment
                      </Select.Option>
                      <Select.Option value="business">Business</Select.Option>
                      <Select.Option value="investment">
                        Investment
                      </Select.Option>
                      <Select.Option value="inheritance">
                        Inheritance
                      </Select.Option>
                      <Select.Option value="other">Other</Select.Option>
                    </Select>
                  </Form.Item>
                </div>
              </div>
            </div>

            {/* 继续添加控制和受益人信息 */}
            <div
              style={{
                width: 804,
                padding: 24,
                background: "white",
                borderRadius: 12,
                border: "1px solid #E4E7EC",
                marginBottom: 32,
                display: "flex",
                flexDirection: "column",
                gap: 32,
              }}
            >
              {/* Nature of Control */}
              <div
                style={{ display: "flex", flexDirection: "column", gap: 12 }}
              >
                <div
                  style={{
                    color: "#081021",
                    fontSize: 14,
                    fontFamily: "Inter",
                    fontWeight: 500,
                    lineHeight: "18px",
                    letterSpacing: "0.14px",
                  }}
                >
                  Nature of the Control over the Malaysia Entity
                </div>
                <Form.Item name="natureOfControl" style={{ marginBottom: 0 }}>
                  <Radio.Group>
                    <div style={{ display: "flex", gap: 40 }}>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: 12,
                        }}
                      >
                        <Radio value="direct">
                          <span
                            style={{
                              color: "#667085",
                              fontSize: 16,
                              fontFamily: "Inter",
                              fontWeight: 400,
                              lineHeight: "20px",
                              letterSpacing: "0.16px",
                            }}
                          >
                            Direct
                          </span>
                        </Radio>
                      </div>
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: 12,
                        }}
                      >
                        <Radio value="indirect">
                          <span
                            style={{
                              color: "#667085",
                              fontSize: 16,
                              fontFamily: "Inter",
                              fontWeight: 400,
                              lineHeight: "20px",
                              letterSpacing: "0.16px",
                            }}
                          >
                            Indirect
                          </span>
                        </Radio>
                      </div>
                    </div>
                  </Radio.Group>
                </Form.Item>
              </div>

              {/* Type of Controlling Person */}
              <div style={{ display: "flex", flexDirection: "column", gap: 6 }}>
                <div
                  style={{
                    color: "#081021",
                    fontSize: 14,
                    fontFamily: "Inter",
                    fontWeight: 500,
                    lineHeight: "18px",
                    letterSpacing: "0.14px",
                  }}
                >
                  Type of Controlling Person
                </div>
                <Form.Item
                  name="typeOfControllingPerson"
                  style={{ marginBottom: 0 }}
                >
                  <Select
                    placeholder="Select Type of Controlling Person"
                    style={{
                      padding: 12,
                      background: "white",
                      boxShadow:
                        "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      borderRadius: 8,
                      border: "1px solid #D0D5DD",
                    }}
                    suffixIcon={
                      <div
                        style={{ width: 16, height: 16, position: "relative" }}
                      >
                        <div
                          style={{
                            width: 11.56,
                            height: 5.73,
                            left: 2.22,
                            top: 5.47,
                            position: "absolute",
                            background: "#475467",
                          }}
                        />
                      </div>
                    }
                  >
                    <Select.Option value="ownership">Ownership</Select.Option>
                    <Select.Option value="voting_rights">
                      Voting Rights
                    </Select.Option>
                    <Select.Option value="control_agreement">
                      Control Agreement
                    </Select.Option>
                    <Select.Option value="other">Other</Select.Option>
                  </Select>
                </Form.Item>
              </div>

              {/* Type of Beneficiary Owner */}
              <div style={{ display: "flex", flexDirection: "column", gap: 6 }}>
                <div
                  style={{
                    color: "#081021",
                    fontSize: 14,
                    fontFamily: "Inter",
                    fontWeight: 500,
                    lineHeight: "18px",
                    letterSpacing: "0.14px",
                  }}
                >
                  Type of Beneficiary Owner
                </div>
                <Form.Item
                  name="typeOfBeneficiaryOwner"
                  style={{ marginBottom: 0 }}
                >
                  <Select
                    placeholder="Select Type of Beneficiary Owner"
                    style={{
                      padding: 12,
                      background: "white",
                      boxShadow:
                        "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      borderRadius: 8,
                      border: "1px solid #D0D5DD",
                    }}
                    suffixIcon={
                      <div
                        style={{ width: 16, height: 16, position: "relative" }}
                      >
                        <div
                          style={{
                            width: 11.56,
                            height: 5.73,
                            left: 2.22,
                            top: 5.47,
                            position: "absolute",
                            background: "#475467",
                          }}
                        />
                      </div>
                    }
                  >
                    <Select.Option value="individual">Individual</Select.Option>
                    <Select.Option value="corporate">Corporate</Select.Option>
                    <Select.Option value="trust">Trust</Select.Option>
                    <Select.Option value="other">Other</Select.Option>
                  </Select>
                </Form.Item>
              </div>
            </div>

            {/* Supporting Documents */}
            <div
              style={{
                padding: 20,
                background: "#F4F7FF",
                borderRadius: 10,
                marginBottom: 32,
                display: "flex",
                flexDirection: "column",
                gap: 16,
              }}
            >
              <div
                style={{
                  color: "#081021",
                  fontSize: 20,
                  fontFamily: "Inter",
                  fontWeight: 500,
                  lineHeight: "24px",
                  letterSpacing: "0.20px",
                }}
              >
                Supporting Documents
              </div>

              <div
                style={{ display: "flex", flexDirection: "column", gap: 16 }}
              >
                <Form.Item
                  name="supportingDocuments"
                  style={{ marginBottom: 0 }}
                >
                  <Dragger
                    multiple
                    style={{
                      height: 200,
                      background: "white",
                      borderRadius: 12,
                      border: "1.5px solid #D0D5DD",
                    }}
                    accept=".png,.jpeg,.jpg,.pdf"
                  >
                    <div style={{ padding: "16px 24px" }}>
                      <div
                        style={{
                          width: 40,
                          height: 40,
                          margin: "0 auto 12px",
                          background: "white",
                          borderRadius: 8,
                          border: "1px solid #D0D5DD",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <InboxOutlined
                          style={{ fontSize: 20, color: "#CECFD2" }}
                        />
                      </div>
                      <div style={{ textAlign: "center", marginBottom: 4 }}>
                        <Button
                          type="link"
                          style={{
                            padding: 2,
                            fontSize: 14,
                            fontFamily: "Inter",
                            fontWeight: 600,
                            color: "#344054",
                          }}
                        >
                          Click to upload
                        </Button>
                        <span
                          style={{
                            color: "#475467",
                            fontSize: 14,
                            fontFamily: "Inter",
                            fontWeight: 400,
                          }}
                        >
                          or drag and drop
                        </span>
                      </div>
                      <div
                        style={{
                          textAlign: "center",
                          color: "#475467",
                          fontSize: 12,
                          fontFamily: "Inter",
                          fontWeight: 400,
                        }}
                      >
                        PNG, JPEG, PDF (max size file limit 5MB)
                      </div>
                    </div>
                  </Dragger>
                </Form.Item>

                {/* Document Requirements List */}
                <div
                  style={{ display: "flex", flexDirection: "column", gap: 12 }}
                >
                  {[
                    "Upload certified copy of address proof issued within last 3 months (i.e utility bill, internet bill, credit card statement, etc.)",
                    "A certified true copy of the latest annual return (or its equivalent) filed with the Registry of Companies/Commercial Registry/Financial Authority",
                    "A certified true copy of the Memorandum & Articles of Association or Company By-Laws/Constitution",
                    "A certified true copy of the Register of Directors and Members",
                    "A Directors' resolution authorising the incorporation of the Labuan Company (corporate shareholder)",
                    "Company ownership structure",
                    "A certified true copy of the certificate of change of name (if any)",
                    "A proof of Indirect Control over Labuan Entity (e.g. agreement, proof of ownership/control)",
                    'A supporting documents for individuals who have received the "Darjah Kebesaran" in Malaysia or any relevant documents for other salutations.',
                  ].map((text, index) => (
                    <div
                      key={index}
                      style={{
                        padding: 12,
                        background: "white",
                        borderRadius: 8,
                        border: "1px solid #E4E7EC",
                      }}
                    >
                      <div
                        style={{
                          color: "#204184",
                          fontSize: 14,
                          fontFamily: "Inter",
                          fontWeight: 500,
                          lineHeight: "18px",
                          letterSpacing: "0.14px",
                        }}
                      >
                        {text}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Address Information */}
            <div style={{ display: "flex", flexDirection: "column", gap: 6 }}>
              <div
                style={{
                  color: "#081021",
                  fontSize: 14,
                  fontFamily: "Inter",
                  fontWeight: 500,
                  lineHeight: "18px",
                  letterSpacing: "0.14px",
                }}
              >
                Address of Registered Office
              </div>
              <Form.Item
                name="registeredOfficeAddress"
                style={{ marginBottom: 0 }}
              >
                <Input.TextArea
                  placeholder="A-21-12, Building ABC, Jalan KL, 51000 Kuala Lumpur"
                  rows={4}
                  style={{
                    padding: "8px 12px",
                    background: "white",
                    boxShadow: "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                    borderRadius: 8,
                    border: "1px solid #D0D5DD",
                    fontSize: 16,
                    fontFamily: "Inter",
                    fontWeight: 400,
                    lineHeight: "20px",
                    letterSpacing: "0.16px",
                    color: "#081021",
                  }}
                />
              </Form.Item>
            </div>

            {/* Correspondence Address */}
            <div style={{ display: "flex", flexDirection: "column", gap: 6 }}>
              <div
                style={{
                  color: "#081021",
                  fontSize: 14,
                  fontFamily: "Inter",
                  fontWeight: 500,
                  lineHeight: "18px",
                  letterSpacing: "0.14px",
                }}
              >
                Correspondence address/Mailing Address
              </div>
              <div style={{ display: "flex", alignItems: "center", gap: 8 }}>
                <Form.Item
                  name="sameAsRegisteredAddress"
                  valuePropName="checked"
                  style={{ marginBottom: 0 }}
                >
                  <Checkbox>
                    <span
                      style={{
                        color: "#081021",
                        fontSize: 14,
                        fontFamily: "Inter",
                        fontWeight: 400,
                        lineHeight: "18px",
                        letterSpacing: "0.14px",
                      }}
                    >
                      Same as Registered Office Address
                    </span>
                  </Checkbox>
                </Form.Item>
              </div>
            </div>

            {/* Authorized Representative Details */}
            <div
              style={{
                padding: 20,
                background: "#F4F7FF",
                borderRadius: 10,
                marginBottom: 32,
                display: "flex",
                flexDirection: "column",
                gap: 16,
              }}
            >
              <div
                style={{
                  color: "#081021",
                  fontSize: 20,
                  fontFamily: "Inter",
                  fontWeight: 500,
                  lineHeight: "24px",
                  letterSpacing: "0.20px",
                }}
              >
                Authorized Representative Details
              </div>

              <div
                style={{ display: "flex", flexDirection: "column", gap: 16 }}
              >
                <Form.Item
                  name="authorizedRepDocuments"
                  style={{ marginBottom: 0 }}
                >
                  <Dragger
                    multiple
                    style={{
                      height: 200,
                      background: "white",
                      borderRadius: 12,
                      border: "1.5px solid #D0D5DD",
                    }}
                    accept=".png,.jpeg,.jpg,.pdf"
                  >
                    <div style={{ padding: "16px 24px" }}>
                      <div
                        style={{
                          width: 40,
                          height: 40,
                          margin: "0 auto 12px",
                          background: "white",
                          borderRadius: 8,
                          border: "1px solid #D0D5DD",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <InboxOutlined
                          style={{ fontSize: 20, color: "#CECFD2" }}
                        />
                      </div>
                      <div style={{ textAlign: "center", marginBottom: 4 }}>
                        <Button
                          type="link"
                          style={{
                            padding: 2,
                            fontSize: 14,
                            fontFamily: "Inter",
                            fontWeight: 600,
                            color: "#344054",
                          }}
                        >
                          Click to upload
                        </Button>
                        <span
                          style={{
                            color: "#475467",
                            fontSize: 14,
                            fontFamily: "Inter",
                            fontWeight: 400,
                          }}
                        >
                          or drag and drop
                        </span>
                      </div>
                      <div
                        style={{
                          textAlign: "center",
                          color: "#475467",
                          fontSize: 12,
                          fontFamily: "Inter",
                          fontWeight: 400,
                        }}
                      >
                        PNG, JPEG, PDF (max size file limit 5MB)
                      </div>
                    </div>
                  </Dragger>
                </Form.Item>

                {/* Authorized Representative Document Requirements */}
                <div
                  style={{ display: "flex", flexDirection: "column", gap: 12 }}
                >
                  {[
                    "Upload a copy of Malaysian identity card (MyKad)/ For foreigners upload Passport.",
                    "Upload certified copy of address proof issued within last 3 months (i.e utility bill, internet bill, credit card statement, etc.)",
                  ].map((text, index) => (
                    <div
                      key={index}
                      style={{
                        padding: 12,
                        background: "white",
                        borderRadius: 8,
                        border: "1px solid #E4E7EC",
                      }}
                    >
                      <div
                        style={{
                          color: "#204184",
                          fontSize: 14,
                          fontFamily: "Inter",
                          fontWeight: 500,
                          lineHeight: "18px",
                          letterSpacing: "0.14px",
                        }}
                      >
                        {text}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <Form.Item className={styles.submitSection}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                className={styles.submitButton}
              >
                Done
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default CorporateShareholderStep;
