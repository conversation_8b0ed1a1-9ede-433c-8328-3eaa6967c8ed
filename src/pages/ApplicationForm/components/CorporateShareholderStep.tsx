import React, { useState } from "react";
import {
  Form,
  Input,
  Select,
  Button,
  Checkbox,
  Radio,
  Space,
  Row,
  Col,
  Upload,
} from "antd";
import { ArrowLeftOutlined, InboxOutlined } from "@ant-design/icons";
import type { CorporateShareholderFormData } from "@/types/applicationForm";
import styles from "./ShareholderDetailStep.module.less";

const { Dragger } = Upload;

interface CorporateShareholderStepProps {
  onNext: (data: CorporateShareholderFormData) => void;
  onBack: () => void;
  initialData?: CorporateShareholderFormData;
  shareholderName?: string;
}

const CorporateShareholderStep: React.FC<CorporateShareholderStepProps> = ({
  onNext,
  onBack,
  initialData,
  shareholderName = "ABC Company",
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      const formData: CorporateShareholderFormData = {
        corporateName: values.corporateName,
        entityNo: values.entityNo,
        incorporationDate: values.incorporationDate,
        address: values.address,
        phoneNumber: values.phoneNumber,
        email: values.email,
        shareholdingPercentage: values.shareholdingPercentage,
        authorizedRepresentative: {
          firstName: values.repFirstName,
          lastName: values.repLastName,
          nricPassport: values.repNricPassport,
          dateOfBirth: values.repDateOfBirth,
          nationality: values.repNationality,
          position: values.repPosition,
        },
      };
      onNext(formData);
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.shareholderDetailStep}>
      <div className={styles.container}>
        <div className={styles.header}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
            className={styles.backButton}
          >
            Back to List
          </Button>
          <h1 className={styles.title}>{shareholderName} - KYC Details</h1>
        </div>

        <div className={styles.formContainer}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={initialData}
            className={styles.form}
          >
            {/* Ultimate Beneficial Owner Question */}
            <div style={{ marginBottom: 32 }}>
              <Form.Item
                label="Does this shareholder have an ultimate beneficial owner?"
                name="hasUBO"
                rules={[{ required: true, message: "Please select an option" }]}
              >
                <Radio.Group>
                  <Space size="large">
                    <Radio value={true}>Yes</Radio>
                    <Radio value={false}>No</Radio>
                  </Space>
                </Radio.Group>
              </Form.Item>
            </div>

            {/* Document Upload Instructions */}
            <div style={{ marginBottom: 32 }}>
              <h3 style={{ fontSize: 18, fontWeight: 500, marginBottom: 16 }}>
                Kindly upload the documents of the ultimate beneficial owner.
              </h3>

              <div style={{ marginBottom: 16 }}>
                <div
                  style={{
                    padding: 12,
                    background: "#E7EFFF",
                    borderRadius: 8,
                    marginBottom: 8,
                  }}
                >
                  <div
                    style={{
                      color: "#204184",
                      fontSize: 14,
                      fontWeight: 600,
                      marginBottom: 4,
                    }}
                  >
                    For Individual Directors and Shareholders:
                  </div>
                  <div
                    style={{ color: "#204184", fontSize: 12, marginBottom: 4 }}
                  >
                    Malaysian: Upload a copy of Malaysian identity card (MyKad).
                  </div>
                  <div style={{ color: "#204184", fontSize: 12 }}>
                    <span style={{ fontWeight: 500 }}>Foreigners</span>: Upload
                    a valid passport with a clear photo and signature.
                  </div>
                </div>

                <div
                  style={{
                    padding: 12,
                    background: "#E7EFFF",
                    borderRadius: 8,
                  }}
                >
                  <div
                    style={{
                      color: "#204184",
                      fontSize: 14,
                      fontWeight: 600,
                      marginBottom: 4,
                    }}
                  >
                    For Corporate Directors & Shareholders:
                  </div>
                  <div style={{ color: "#204184", fontSize: 12 }}>
                    Upload a certified copy of the company's Certificate of
                    Incorporation.
                  </div>
                </div>
              </div>

              {/* Initial Document Upload */}
              <Form.Item name="initialDocuments">
                <Dragger
                  multiple
                  style={{ height: 200 }}
                  accept=".png,.jpeg,.jpg,.pdf"
                >
                  <div style={{ padding: "16px 24px" }}>
                    <div
                      style={{
                        width: 40,
                        height: 40,
                        margin: "0 auto 12px",
                        background: "white",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                      }}
                    >
                      <InboxOutlined
                        style={{ fontSize: 20, color: "#CECFD2" }}
                      />
                    </div>
                    <div style={{ textAlign: "center", marginBottom: 4 }}>
                      <Button
                        type="link"
                        style={{ padding: 2, fontSize: 14, fontWeight: 600 }}
                      >
                        Click to upload
                      </Button>
                      <span style={{ color: "#475467", fontSize: 14 }}>
                        or drag and drop
                      </span>
                    </div>
                    <div
                      style={{
                        textAlign: "center",
                        color: "#475467",
                        fontSize: 12,
                      }}
                    >
                      PNG, JPEG, PDF (max size file limit 5MB)
                    </div>
                  </div>
                </Dragger>
              </Form.Item>
            </div>

            {/* Main Form Card */}
            <div
              style={{
                padding: 24,
                background: "white",
                borderRadius: 12,
                border: "1px solid #E4E7EC",
                marginBottom: 32,
              }}
            >
              {/* Role in Company */}
              <Form.Item
                label="Role in the Company"
                name="roles"
                style={{ marginBottom: 32 }}
              >
                <Checkbox.Group>
                  <Space size={12}>
                    <div
                      style={{
                        minWidth: 189,
                        padding: "20px 16px",
                        background: "white",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        display: "flex",
                        alignItems: "center",
                        gap: 12,
                      }}
                    >
                      <Checkbox value="director">Director</Checkbox>
                    </div>
                    <div
                      style={{
                        minWidth: 189,
                        padding: "20px 16px",
                        background: "white",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        display: "flex",
                        alignItems: "center",
                        gap: 12,
                      }}
                    >
                      <Checkbox value="shareholder">Shareholder</Checkbox>
                    </div>
                    <div
                      style={{
                        minWidth: 189,
                        padding: "20px 16px",
                        background: "white",
                        borderRadius: 8,
                        border: "1px solid #D0D5DD",
                        display: "flex",
                        alignItems: "center",
                        gap: 12,
                      }}
                    >
                      <Checkbox value="ultimate_beneficial_owners">
                        Ultimate Beneficial Owners
                      </Checkbox>
                    </div>
                  </Space>
                </Checkbox.Group>
              </Form.Item>

              {/* Share Information */}
              <Row gutter={40} style={{ marginBottom: 32 }}>
                <Col span={12}>
                  <Form.Item
                    label="Number & Percentage of share(s) to be issued"
                    name="sharePercentage"
                    rules={[
                      { required: true, message: "Please enter percentage" },
                    ]}
                  >
                    <Input
                      placeholder="%"
                      suffix="%"
                      style={{
                        padding: 12,
                        borderRadius: 8,
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="UBO Percentage of shares"
                    name="uboPercentage"
                    rules={[
                      {
                        required: true,
                        message: "Please enter UBO percentage",
                      },
                    ]}
                  >
                    <Input
                      placeholder="%"
                      suffix="%"
                      style={{
                        padding: 12,
                        borderRadius: 8,
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Jurisdiction */}
              <Form.Item
                label="Jurisdiction"
                name="jurisdiction"
                rules={[
                  { required: true, message: "Please enter jurisdiction" },
                ]}
                style={{ marginBottom: 32 }}
              >
                <Input
                  placeholder="Enter Jurisdiction"
                  style={{
                    padding: 12,
                    borderRadius: 8,
                    boxShadow: "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                  }}
                />
              </Form.Item>

              {/* Nature of Business */}
              <Form.Item
                label="Nature of Business"
                name="natureOfBusiness"
                rules={[
                  {
                    required: true,
                    message: "Please enter nature of business",
                  },
                ]}
                style={{ marginBottom: 32 }}
              >
                <Input
                  placeholder="Enter Nature of Business"
                  style={{
                    padding: 12,
                    borderRadius: 8,
                    boxShadow: "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                  }}
                />
              </Form.Item>

              {/* Position and Email */}
              <Row gutter={40} style={{ marginBottom: 32 }}>
                <Col span={12}>
                  <Form.Item
                    label="Position in Labuan Entity"
                    name="positionInEntity"
                    rules={[
                      { required: true, message: "Please select position" },
                    ]}
                  >
                    <Select
                      placeholder="Select Position"
                      style={{
                        borderRadius: 8,
                      }}
                    >
                      <Select.Option value="director">Director</Select.Option>
                      <Select.Option value="shareholder">
                        Shareholder
                      </Select.Option>
                      <Select.Option value="secretary">Secretary</Select.Option>
                      <Select.Option value="other">Other</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Email"
                    name="email"
                    rules={[
                      { required: true, message: "Please enter email" },
                      { type: "email", message: "Please enter valid email" },
                    ]}
                  >
                    <Input
                      placeholder="Enter Email"
                      style={{
                        padding: 12,
                        borderRadius: 8,
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Phone Numbers */}
              <Row gutter={40} style={{ marginBottom: 32 }}>
                <Col span={12}>
                  <Form.Item
                    label="Telephone Number"
                    name="telephoneNumber"
                    rules={[
                      {
                        required: true,
                        message: "Please enter telephone number",
                      },
                    ]}
                  >
                    <Input
                      placeholder="Enter Telephone Number"
                      style={{
                        padding: 12,
                        borderRadius: 8,
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Mobile Number"
                    name="mobileNumber"
                    rules={[
                      { required: true, message: "Please enter mobile number" },
                    ]}
                  >
                    <Input
                      placeholder="Enter Mobile Number"
                      style={{
                        padding: 12,
                        borderRadius: 8,
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Tax Information */}
              <Row gutter={40} style={{ marginBottom: 32 }}>
                <Col span={12}>
                  <Form.Item
                    label="Tax Residency"
                    name="taxResidency"
                    rules={[
                      { required: true, message: "Please enter tax residency" },
                    ]}
                  >
                    <Input
                      placeholder="Enter Tax residency"
                      style={{
                        padding: 12,
                        borderRadius: 8,
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="TIN"
                    name="tin"
                    rules={[{ required: true, message: "Please enter TIN" }]}
                  >
                    <Input
                      placeholder="Enter Tax No"
                      style={{
                        padding: 12,
                        borderRadius: 8,
                        boxShadow:
                          "0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)",
                      }}
                    />
                  </Form.Item>
                </Col>
              </Row>

              {/* Financial Information */}
              <Row gutter={40} style={{ marginBottom: 32 }}>
                <Col span={12}>
                  <Form.Item
                    label="Estimated Net Worth"
                    name="estimatedNetWorth"
                    rules={[
                      {
                        required: true,
                        message: "Please select estimated net worth",
                      },
                    ]}
                  >
                    <Select placeholder="Select" style={{ borderRadius: 8 }}>
                      <Select.Option value="below_100k">
                        Below $100,000
                      </Select.Option>
                      <Select.Option value="100k_500k">
                        $100,000 - $500,000
                      </Select.Option>
                      <Select.Option value="500k_1m">
                        $500,000 - $1,000,000
                      </Select.Option>
                      <Select.Option value="1m_5m">
                        $1,000,000 - $5,000,000
                      </Select.Option>
                      <Select.Option value="above_5m">
                        Above $5,000,000
                      </Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Annual Income/Annual Revenue"
                    name="annualIncome"
                    rules={[
                      {
                        required: true,
                        message: "Please select annual income",
                      },
                    ]}
                  >
                    <Select placeholder="Select" style={{ borderRadius: 8 }}>
                      <Select.Option value="below_50k">
                        Below $50,000
                      </Select.Option>
                      <Select.Option value="50k_100k">
                        $50,000 - $100,000
                      </Select.Option>
                      <Select.Option value="100k_500k">
                        $100,000 - $500,000
                      </Select.Option>
                      <Select.Option value="500k_1m">
                        $500,000 - $1,000,000
                      </Select.Option>
                      <Select.Option value="above_1m">
                        Above $1,000,000
                      </Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              {/* Source of Wealth */}
              <Form.Item
                label="Source of Wealth"
                name="sourceOfWealth"
                rules={[
                  { required: true, message: "Please select source of wealth" },
                ]}
                style={{ marginBottom: 32, maxWidth: 358 }}
              >
                <Select placeholder="Select" style={{ borderRadius: 8 }}>
                  <Select.Option value="employment">Employment</Select.Option>
                  <Select.Option value="business">Business</Select.Option>
                  <Select.Option value="investment">Investment</Select.Option>
                  <Select.Option value="inheritance">Inheritance</Select.Option>
                  <Select.Option value="other">Other</Select.Option>
                </Select>
              </Form.Item>

              {/* Nature of Control */}
              <Form.Item
                label="Nature of the Control over the Labuan Entity"
                name="natureOfControl"
                rules={[
                  {
                    required: true,
                    message: "Please select nature of control",
                  },
                ]}
                style={{ marginBottom: 32 }}
              >
                <Radio.Group>
                  <Space size="large">
                    <Radio value="direct">Direct</Radio>
                    <Radio value="indirect">Indirect</Radio>
                  </Space>
                </Radio.Group>
              </Form.Item>

              {/* Type of Controlling Person */}
              <Form.Item
                label="Type of Controlling Person"
                name="typeOfControllingPerson"
                rules={[
                  {
                    required: true,
                    message: "Please select type of controlling person",
                  },
                ]}
                style={{ marginBottom: 32 }}
              >
                <Select
                  placeholder="Select Type of Controlling Person"
                  style={{ borderRadius: 8 }}
                >
                  <Select.Option value="ownership">Ownership</Select.Option>
                  <Select.Option value="voting_rights">
                    Voting Rights
                  </Select.Option>
                  <Select.Option value="control_agreement">
                    Control Agreement
                  </Select.Option>
                  <Select.Option value="other">Other</Select.Option>
                </Select>
              </Form.Item>

              {/* Type of Beneficiary Owner */}
              <Form.Item
                label="Type of Beneficiary Owner"
                name="typeOfBeneficiaryOwner"
                rules={[
                  {
                    required: true,
                    message: "Please select type of beneficiary owner",
                  },
                ]}
                style={{ marginBottom: 32 }}
              >
                <Select
                  placeholder="Select Type of Beneficiary Owner"
                  style={{ borderRadius: 8 }}
                >
                  <Select.Option value="individual">Individual</Select.Option>
                  <Select.Option value="corporate">Corporate</Select.Option>
                  <Select.Option value="trust">Trust</Select.Option>
                  <Select.Option value="other">Other</Select.Option>
                </Select>
              </Form.Item>

              {/* Supporting Documents */}
              <div
                style={{
                  padding: 20,
                  background: "#F4F7FF",
                  borderRadius: 10,
                  marginBottom: 32,
                }}
              >
                <h3
                  style={{
                    fontSize: 20,
                    fontWeight: 500,
                    marginBottom: 16,
                    color: "#081021",
                  }}
                >
                  Supporting Documents
                </h3>

                <Form.Item name="supportingDocuments">
                  <Dragger
                    multiple
                    style={{
                      height: 200,
                      marginBottom: 16,
                      background: "white",
                      borderRadius: 12,
                      border: "1.5px solid #D0D5DD",
                    }}
                    accept=".png,.jpeg,.jpg,.pdf"
                  >
                    <div style={{ padding: "16px 24px" }}>
                      <div
                        style={{
                          width: 40,
                          height: 40,
                          margin: "0 auto 12px",
                          background: "white",
                          borderRadius: 8,
                          border: "1px solid #D0D5DD",
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                        }}
                      >
                        <InboxOutlined
                          style={{ fontSize: 20, color: "#CECFD2" }}
                        />
                      </div>
                      <div style={{ textAlign: "center", marginBottom: 4 }}>
                        <Button
                          type="link"
                          style={{ padding: 2, fontSize: 14, fontWeight: 600 }}
                        >
                          Click to upload
                        </Button>
                        <span style={{ color: "#475467", fontSize: 14 }}>
                          or drag and drop
                        </span>
                      </div>
                      <div
                        style={{
                          textAlign: "center",
                          color: "#475467",
                          fontSize: 12,
                        }}
                      >
                        PNG, JPEG, PDF (max size file limit 5MB)
                      </div>
                    </div>
                  </Dragger>
                </Form.Item>

                {/* Document Requirements List */}
                <div
                  style={{ display: "flex", flexDirection: "column", gap: 12 }}
                >
                  <div
                    style={{
                      padding: 12,
                      background: "white",
                      borderRadius: 8,
                      border: "1px solid #E4E7EC",
                    }}
                  >
                    <div
                      style={{
                        color: "#204184",
                        fontSize: 14,
                        fontWeight: 500,
                      }}
                    >
                      A certified copy of address proof issued within last 3
                      months (i.e utility bill, internet bill, credit card
                      statement, etc.)
                    </div>
                  </div>

                  <div
                    style={{
                      padding: 12,
                      background: "white",
                      borderRadius: 8,
                      border: "1px solid #E4E7EC",
                    }}
                  >
                    <div
                      style={{
                        color: "#204184",
                        fontSize: 14,
                        fontWeight: 500,
                      }}
                    >
                      A certified true copy of the latest annual return (or its
                      equivalent) filed with the Registry of
                      Companies/Commercial Registry/Financial Authority
                    </div>
                  </div>

                  <div
                    style={{
                      padding: 12,
                      background: "white",
                      borderRadius: 8,
                      border: "1px solid #E4E7EC",
                    }}
                  >
                    <div
                      style={{
                        color: "#204184",
                        fontSize: 14,
                        fontWeight: 500,
                      }}
                    >
                      A certified true copy of the Memorandum & Articles of
                      Association or Company By-Laws/Constitution
                    </div>
                  </div>

                  <div
                    style={{
                      padding: 12,
                      background: "white",
                      borderRadius: 8,
                      border: "1px solid #E4E7EC",
                    }}
                  >
                    <div
                      style={{
                        color: "#204184",
                        fontSize: 14,
                        fontWeight: 500,
                      }}
                    >
                      A certified true copy of the Register of Directors and
                      Members
                    </div>
                  </div>

                  <div
                    style={{
                      padding: 12,
                      background: "white",
                      borderRadius: 8,
                      border: "1px solid #E4E7EC",
                    }}
                  >
                    <div
                      style={{
                        color: "#204184",
                        fontSize: 14,
                        fontWeight: 500,
                      }}
                    >
                      A Directors' resolution authorising the incorporation of
                      the Labuan Company (corporate shareholder)
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Submit Button */}
            <Form.Item className={styles.submitSection}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                className={styles.submitButton}
              >
                Done
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default CorporateShareholderStep;
