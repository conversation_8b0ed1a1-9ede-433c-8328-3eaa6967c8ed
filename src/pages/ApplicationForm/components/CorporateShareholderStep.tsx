import React, { useState } from "react";
import {
  Form,
  Input,
  Select,
  Button,
  Checkbox,
  Radio,
  Space,
  Row,
  Col,
  Divider,
} from "antd";
import { ArrowLeftOutlined } from "@ant-design/icons";
import type { CorporateShareholderFormData } from "@/types/applicationForm";
import styles from "./ShareholderDetailStep.module.less";

interface CorporateShareholderStepProps {
  onNext: (data: CorporateShareholderFormData) => void;
  onBack: () => void;
  initialData?: CorporateShareholderFormData;
  shareholderName?: string;
}

const CorporateShareholderStep: React.FC<CorporateShareholderStepProps> = ({
  onNext,
  onBack,
  initialData,
  shareholderName = "ABC Company",
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      const formData: CorporateShareholderFormData = {
        corporateName: values.corporateName,
        entityNo: values.entityNo,
        incorporationDate: values.incorporationDate,
        address: values.address,
        phoneNumber: values.phoneNumber,
        email: values.email,
        shareholdingPercentage: values.shareholdingPercentage,
        authorizedRepresentative: {
          firstName: values.repFirstName,
          lastName: values.repLastName,
          nricPassport: values.repNricPassport,
          dateOfBirth: values.repDateOfBirth,
          nationality: values.repNationality,
          position: values.repPosition,
        },
      };
      onNext(formData);
    } catch (error) {
      console.error("Form submission error:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.shareholderDetailStep}>
      <div className={styles.container}>
        <div className={styles.header}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={onBack}
            className={styles.backButton}
          >
            Back to List
          </Button>
          <h1 className={styles.title}>{shareholderName} - KYC Details</h1>
        </div>

        <div className={styles.formContainer}>
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={initialData}
            className={styles.form}
          >
            {/* Role in Company */}
            <Form.Item label="Role in the Company" name="roles">
              <Checkbox.Group>
                <Space direction="horizontal" size="large">
                  <div className={styles.checkboxCard}>
                    <Checkbox value="director">Director</Checkbox>
                  </div>
                  <div className={styles.checkboxCard}>
                    <Checkbox value="shareholder">Shareholder</Checkbox>
                  </div>
                </Space>
              </Checkbox.Group>
            </Form.Item>

            {/* Corporate Information */}
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="Corporate Name"
                  name="corporateName"
                  rules={[
                    { required: true, message: "Please enter corporate name" },
                  ]}
                >
                  <Input placeholder="Enter corporate name" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Entity No."
                  name="entityNo"
                  rules={[
                    { required: true, message: "Please enter entity number" },
                  ]}
                >
                  <Input placeholder="Enter entity number" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="Date of Incorporation"
                  name="incorporationDate"
                  rules={[
                    {
                      required: true,
                      message: "Please enter incorporation date",
                    },
                  ]}
                >
                  <Input placeholder="DD/MM/YYYY" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Country of Incorporation"
                  name="countryOfIncorporation"
                  rules={[{ required: true, message: "Please select country" }]}
                >
                  <Select placeholder="Select country">
                    <Select.Option value="malaysia">Malaysia</Select.Option>
                    <Select.Option value="singapore">Singapore</Select.Option>
                    <Select.Option value="indonesia">Indonesia</Select.Option>
                    <Select.Option value="thailand">Thailand</Select.Option>
                    <Select.Option value="other">Other</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            {/* Contact Information */}
            <Form.Item
              label="Registered Address"
              name="address"
              rules={[
                { required: true, message: "Please enter registered address" },
              ]}
            >
              <Input.TextArea
                rows={3}
                placeholder="Enter full registered address"
              />
            </Form.Item>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="Phone Number"
                  name="phoneNumber"
                  rules={[
                    { required: true, message: "Please enter phone number" },
                  ]}
                >
                  <Input placeholder="Enter phone number" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Email Address"
                  name="email"
                  rules={[
                    { required: true, message: "Please enter email address" },
                    {
                      type: "email",
                      message: "Please enter valid email address",
                    },
                  ]}
                >
                  <Input placeholder="Enter email address" />
                </Form.Item>
              </Col>
            </Row>

            {/* Business Information */}
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="Shareholding Percentage"
                  name="shareholdingPercentage"
                  rules={[
                    {
                      required: true,
                      message: "Please enter shareholding percentage",
                    },
                  ]}
                >
                  <Input
                    type="number"
                    min={0}
                    max={100}
                    placeholder="Enter percentage"
                    suffix="%"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Nature of Business"
                  name="natureOfBusiness"
                  rules={[
                    {
                      required: true,
                      message: "Please enter nature of business",
                    },
                  ]}
                >
                  <Input placeholder="Enter nature of business" />
                </Form.Item>
              </Col>
            </Row>

            <Divider orientation="left">Authorized Representative</Divider>

            {/* Authorized Representative */}
            <Row gutter={24}>
              <Col span={6}>
                <Form.Item
                  label="Salutation"
                  name="repSalutation"
                  rules={[
                    { required: true, message: "Please select salutation" },
                  ]}
                >
                  <Select placeholder="Select">
                    <Select.Option value="mr">Mr</Select.Option>
                    <Select.Option value="mrs">Mrs</Select.Option>
                    <Select.Option value="ms">Ms</Select.Option>
                    <Select.Option value="dr">Dr</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={9}>
                <Form.Item
                  label="First Name"
                  name="repFirstName"
                  rules={[
                    { required: true, message: "Please enter first name" },
                  ]}
                >
                  <Input placeholder="Enter first name" />
                </Form.Item>
              </Col>
              <Col span={9}>
                <Form.Item
                  label="Last Name"
                  name="repLastName"
                  rules={[
                    { required: true, message: "Please enter last name" },
                  ]}
                >
                  <Input placeholder="Enter last name" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="NRIC/Passport Number"
                  name="repNricPassport"
                  rules={[
                    {
                      required: true,
                      message: "Please enter NRIC/Passport number",
                    },
                  ]}
                >
                  <Input placeholder="Enter NRIC/Passport number" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Date of Birth"
                  name="repDateOfBirth"
                  rules={[
                    { required: true, message: "Please enter date of birth" },
                  ]}
                >
                  <Input placeholder="DD/MM/YYYY" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="Nationality"
                  name="repNationality"
                  rules={[
                    { required: true, message: "Please select nationality" },
                  ]}
                >
                  <Select placeholder="Select nationality">
                    <Select.Option value="malaysia">Malaysia</Select.Option>
                    <Select.Option value="singapore">Singapore</Select.Option>
                    <Select.Option value="indonesia">Indonesia</Select.Option>
                    <Select.Option value="thailand">Thailand</Select.Option>
                    <Select.Option value="other">Other</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Position in Company"
                  name="repPosition"
                  rules={[{ required: true, message: "Please enter position" }]}
                >
                  <Input placeholder="Enter position" />
                </Form.Item>
              </Col>
            </Row>

            {/* PEP Status */}
            <Form.Item
              label="Has this entity or its authorized representative represented or previously represented any Political Organization or individuals who are Politically Exposed Persons (PEPs)?"
              name="isPEP"
              rules={[{ required: true, message: "Please select an option" }]}
            >
              <Radio.Group>
                <Space size="large">
                  <Radio value={true}>Yes</Radio>
                  <Radio value={false}>No</Radio>
                </Space>
              </Radio.Group>
            </Form.Item>

            {/* Submit Button */}
            <Form.Item className={styles.submitSection}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                size="large"
                className={styles.submitButton}
              >
                Done
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default CorporateShareholderStep;
