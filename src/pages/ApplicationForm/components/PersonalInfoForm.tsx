import React from 'react';
import { Row, Col } from 'antd';
import Form<PERSON><PERSON> from './FormField';

interface PersonalInfoFormProps {
  prefix?: string;
  showSalutation?: boolean;
  showPlaceOfBirth?: boolean;
  showPosition?: boolean;
}

const PersonalInfoForm: React.FC<PersonalInfoFormProps> = ({
  prefix = '',
  showSalutation = true,
  showPlaceOfBirth = false,
  showPosition = false,
}) => {
  const fieldName = (name: string) => prefix ? `${prefix}${name.charAt(0).toUpperCase() + name.slice(1)}` : name;

  const salutationOptions = [
    { value: 'mr', label: 'Mr' },
    { value: 'mrs', label: 'Mrs' },
    { value: 'ms', label: 'Ms' },
    { value: 'dr', label: 'Dr' },
  ];

  const nationalityOptions = [
    { value: 'malaysia', label: 'Malaysia' },
    { value: 'singapore', label: 'Singapore' },
    { value: 'indonesia', label: 'Indonesia' },
    { value: 'thailand', label: 'Thailand' },
    { value: 'other', label: 'Other' },
  ];

  return (
    <>
      <Row gutter={24}>
        {showSalutation && (
          <FormField
            label="Salutation"
            name={fieldName('salutation')}
            type="select"
            options={salutationOptions}
            rules={[{ required: true, message: 'Please select salutation' }]}
            span={6}
          />
        )}
        <FormField
          label="First Name"
          name={fieldName('firstName')}
          placeholder="Enter first name"
          rules={[{ required: true, message: 'Please enter first name' }]}
          span={showSalutation ? 9 : 12}
        />
        <FormField
          label="Last Name"
          name={fieldName('lastName')}
          placeholder="Enter last name"
          rules={[{ required: true, message: 'Please enter last name' }]}
          span={showSalutation ? 9 : 12}
        />
      </Row>

      <Row gutter={24}>
        <FormField
          label="NRIC/Passport Number"
          name={fieldName('nricPassport')}
          placeholder="Enter NRIC/Passport number"
          rules={[{ required: true, message: 'Please enter NRIC/Passport number' }]}
          span={12}
        />
        <FormField
          label="Date of Birth"
          name={fieldName('dateOfBirth')}
          placeholder="DD/MM/YYYY"
          rules={[{ required: true, message: 'Please enter date of birth' }]}
          span={12}
        />
      </Row>

      <Row gutter={24}>
        <FormField
          label="Nationality"
          name={fieldName('nationality')}
          type="select"
          options={nationalityOptions}
          rules={[{ required: true, message: 'Please select nationality' }]}
          span={12}
        />
        {showPlaceOfBirth && (
          <FormField
            label="Place of Birth"
            name={fieldName('placeOfBirth')}
            placeholder="Enter place of birth"
            rules={[{ required: true, message: 'Please enter place of birth' }]}
            span={12}
          />
        )}
        {showPosition && (
          <FormField
            label="Position in Company"
            name={fieldName('position')}
            placeholder="Enter position"
            rules={[{ required: true, message: 'Please enter position' }]}
            span={12}
          />
        )}
      </Row>
    </>
  );
};

export default PersonalInfoForm;
