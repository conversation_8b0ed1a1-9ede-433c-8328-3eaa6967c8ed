// CompanyDetailsStep 组件样式
.companyDetailsStep {
  width: 100%;
  min-height: calc(100vh - 93px);
  background: #FCFCFE;
  display: flex;
  justify-content: center;
  padding: 40px 20px;

  .container {
    width: 804px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .header {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .title {
        color: var(--Text-text-primary-(900), #081021);
        font-size: 20px;
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        line-height: 24px;
        letter-spacing: 0.20px;
        margin: 0;
      }
    }

    .formCard {
      padding: 24px;
      background: var(--Background-bg-primary, white);
      box-shadow: 0px 1px 8px rgba(24.73, 32.61, 60.56, 0.08);
      border-radius: 12px;
      border: 1px solid var(--Border-border-secondary, #E4E7EC);

      .form {
        display: flex;
        flex-direction: column;
        gap: 32px;

        .inputField {
          padding: 12px;
          background: var(--Background-bg-primary, white);
          box-shadow: 0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05);
          border-radius: 8px;
          border: 1px solid var(--Border-border-primary, #D0D5DD);
          font-size: 16px;
          font-family: 'Inter', sans-serif;
          font-weight: 400;
          line-height: 20px;
          letter-spacing: 0.16px;

          &::placeholder {
            color: var(--Text-text-placeholder, #667085);
          }

          &:focus {
            border-color: var(--Background-bg-brand-solid, #1B3770);
            box-shadow: 0 0 0 2px rgba(27, 55, 112, 0.1);
          }
        }

        .selectField {
          :global(.ant-select-selector) {
            padding: 12px !important;
            background: var(--Background-bg-primary, white);
            box-shadow: 0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05);
            border-radius: 8px !important;
            border: 1px solid var(--Border-border-primary, #D0D5DD) !important;
            font-size: 16px;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            line-height: 20px;
            letter-spacing: 0.16px;
            height: auto !important;
            min-height: 44px;

            :global(.ant-select-selection-placeholder) {
              color: var(--Text-text-placeholder, #667085);
            }
          }

          &:global(.ant-select-focused) {
            :global(.ant-select-selector) {
              border-color: var(--Background-bg-brand-solid, #1B3770) !important;
              box-shadow: 0 0 0 2px rgba(27, 55, 112, 0.1) !important;
            }
          }

          :global(.ant-select-arrow) {
            color: var(--Icon-icon-fg-gray, #475467);
          }
        }

        .textareaField {
          padding: 8px 12px;
          background: var(--Background-bg-primary, white);
          box-shadow: 0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05);
          border-radius: 8px;
          border: 1px solid var(--Border-border-primary, #D0D5DD);
          font-size: 16px;
          font-family: 'Inter', sans-serif;
          font-weight: 400;
          line-height: 20px;
          letter-spacing: 0.16px;
          resize: vertical;

          &::placeholder {
            color: var(--Text-text-placeholder, #667085);
          }

          &:focus {
            border-color: var(--Background-bg-brand-solid, #1B3770);
            box-shadow: 0 0 0 2px rgba(27, 55, 112, 0.1);
          }
        }

        .radioGroup {
          display: flex;
          gap: 48px;

          :global(.ant-radio-wrapper) {
            color: var(--Text-text-placeholder, #667085);
            font-size: 16px;
            font-family: 'Inter', sans-serif;
            font-weight: 400;
            line-height: 20px;
            letter-spacing: 0.16px;

            :global(.ant-radio) {
              :global(.ant-radio-inner) {
                width: 16px;
                height: 16px;
                border-color: var(--Border-border-primary, #D0D5DD);
              }

              &:global(.ant-radio-checked) {
                :global(.ant-radio-inner) {
                  background-color: var(--Background-bg-brand-primary, #DEE3ED);
                  border-color: var(--Border-border-primary, #D0D5DD);

                  &::after {
                    background-color: var(--Background-bg-brand-solid, #1B3770);
                  }
                }
              }
            }
          }
        }

        // Form Item 样式覆盖
        :global(.ant-form-item) {
          margin-bottom: 0;

          :global(.ant-form-item-label) {
            padding-bottom: 6px;

            > label {
              color: var(--Text-text-primary-(900), #081021);
              font-size: 14px;
              font-family: 'Inter', sans-serif;
              font-weight: 500;
              line-height: 18px;
              letter-spacing: 0.14px;
              height: auto;
            }
          }

          :global(.ant-form-item-explain-error) {
            font-size: 12px;
            margin-top: 4px;
          }
        }

        // Row 和 Col 样式
        :global(.ant-row) {
          margin-left: 0 !important;
          margin-right: 0 !important;

          :global(.ant-col) {
            padding-left: 0 !important;
            padding-right: 0 !important;
          }
        }
      }
    }

    .buttonGroup {
      display: flex;
      justify-content: space-between;
      gap: 20px;

      .backButton,
      .nextButton {
        width: 300px;
        height: 48px;
        border-radius: 10px;
        font-size: 16px;
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 0.16px;
      }

      .backButton {
        background: var(--Button-button-secondary-color-bg, white);
        border: 1px solid var(--Button-button-secondary-color-border, #5871A3);
        color: var(--Button-button-secondary-color-fg, #1B3770);

        &:hover {
          background: #f8f9fb !important;
          border-color: #4a6491 !important;
          color: #1B3770 !important;
        }
      }

      .nextButton {
        background: var(--Button-button-primary-bg, #204184);
        border: none;
        color: var(--Button-button-primary-fg, white);
        box-shadow: 0px 3px 4px rgba(223.02, 237.74, 255, 0.10) inset;

        &:hover {
          background: #1a3670 !important;
        }

        &:focus {
          background: #1a3670 !important;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .companyDetailsStep {
    padding: 20px 16px;

    .container {
      width: 100%;

      .header {
        .title {
          font-size: 18px;
          line-height: 22px;
        }
      }

      .formCard {
        padding: 20px;

        .form {
          gap: 24px;

          .radioGroup {
            gap: 24px;
            flex-direction: column;
          }
        }
      }

      .buttonGroup {
        flex-direction: column;

        .backButton,
        .nextButton {
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .companyDetailsStep {
    padding: 16px 12px;

    .container {
      .formCard {
        padding: 16px;
      }

      .buttonGroup {
        .backButton,
        .nextButton {
          font-size: 14px;
          height: 44px;
        }
      }
    }
  }
}
