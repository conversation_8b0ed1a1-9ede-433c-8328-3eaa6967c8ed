import React, { useState, useEffect } from "react";
import { <PERSON>ton, Tag, App, Spin } from "antd";
import { DownOutlined } from "@ant-design/icons";
import { ApplicationHeader } from "@/components";
import styles from "./OverviewStep.module.less";

interface OverviewStepProps {
  onNext: () => void;
  onBack: () => void;
}

interface CompanyDetails {
  firstOptionName: string;
  firstOptionForeignName: string;
  firstClarification: string;
  secondOptionName: string;
  secondOptionForeignName: string;
  secondClarification: string;
  thirdOptionName: string;
  thirdOptionForeignName: string;
  thirdClarification: string;
  numberOfShares: string;
  issuePricePerShare: string;
  sourceOfInitialFund: string;
  relevantBusinessActivities: string;
  labuanLicenseTradingActivity: string;
  describeNatureOfBusiness: string;
  countriesOfBusiness: string;
  numberOfWorkingPermit: string;
  numberOfDependentPass: string;
  expectedAnnualRevenue: string;
  valueOfAsset: string;
  expectedNumberOfEmployees: string;
}

interface ShareholderDetails {
  id: string;
  name: string;
  nricPassport: string;
  nationality: string;
  percentageOfShares: string;
  roles: string[];
}

const OverviewStep: React.FC<OverviewStepProps> = ({ onNext, onBack }) => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState(true);
  const [companyDetails, setCompanyDetails] = useState<CompanyDetails | null>(
    null
  );
  const [shareholderDetails, setShareholders] = useState<ShareholderDetails[]>(
    []
  );
  const [expandedCards, setExpandedCards] = useState<Set<string>>(new Set());

  useEffect(() => {
    // 模拟加载数据
    const loadData = async () => {
      setLoading(true);
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 模拟公司详情数据
      setCompanyDetails({
        firstOptionName: "ABCD",
        firstOptionForeignName: "ABCD",
        firstClarification: "ABCD",
        secondOptionName: "ABCD",
        secondOptionForeignName: "ABCD",
        secondClarification: "ABCD",
        thirdOptionName: "ABCD",
        thirdOptionForeignName: "ABCD",
        thirdClarification: "ABCD",
        numberOfShares: "1,000",
        issuePricePerShare: "USD 1.00",
        sourceOfInitialFund: "ABC",
        relevantBusinessActivities: "ABC",
        labuanLicenseTradingActivity: "ABC",
        describeNatureOfBusiness: "ABC",
        countriesOfBusiness: "ABC",
        numberOfWorkingPermit: "ABC",
        numberOfDependentPass: "ABC",
        expectedAnnualRevenue: "ABC",
        valueOfAsset: "ABC",
        expectedNumberOfEmployees: "ABC",
      });

      // 模拟股东详情数据
      setShareholders([
        {
          id: "1",
          name: "Mr Tan Wei Ming",
          nricPassport: "999999-99-9999",
          nationality: "Malaysian",
          percentageOfShares: "30%",
          roles: ["Director", "Shareholder", "UBO"],
        },
        {
          id: "2",
          name: "Mr Tan Wei Ming",
          nricPassport: "999999-99-9999",
          nationality: "Malaysian",
          percentageOfShares: "30%",
          roles: ["Director", "Shareholder", "UBO"],
        },
        {
          id: "3",
          name: "Mr Tan Wei Ming",
          nricPassport: "999999-99-9999",
          nationality: "Malaysian",
          percentageOfShares: "30%",
          roles: ["Director", "Shareholder", "UBO"],
        },
      ]);

      setLoading(false);
    };

    loadData();
  }, []);

  const toggleCardExpansion = (cardId: string) => {
    setExpandedCards((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(cardId)) {
        newSet.delete(cardId);
      } else {
        newSet.add(cardId);
      }
      return newSet;
    });
  };

  const getRoleTagColor = (role: string) => {
    switch (role) {
      case "Director":
        return { background: "#FBD3FE", color: "#6C0075" };
      case "Shareholder":
        return { background: "#B1D5FF", color: "#003675" };
      case "UBO":
        return { background: "#FFE9D3", color: "#6F3800" };
      default:
        return { background: "#F2F4F7", color: "#344054" };
    }
  };

  const handleNext = () => {
    onNext();
  };

  if (loading) {
    return (
      <div className={styles.overviewStep}>
        <div className={styles.container}>
          <div className={styles.loadingContainer}>
            <Spin size="large" />
            <div>Loading overview...</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.overviewStep}>
      <div className={styles.container}>
        <div className={styles.content}>
          {/* Company Details Section */}
          <div className={styles.section}>
            <div className={styles.title}>
              <h2>Company Details</h2>
            </div>
            <div className={styles.card}>
              <div className={styles.cardContent}>
                {/* First Option */}
                <div className={styles.optionGroup}>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>First Option Name</div>
                      <div className={styles.value}>
                        {companyDetails?.firstOptionName}
                      </div>
                    </div>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        First Option Foreign Name
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.firstOptionForeignName}
                      </div>
                    </div>
                  </div>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Clarification for Abbreviation
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.firstClarification}
                      </div>
                    </div>
                  </div>
                </div>

                <div className={styles.divider}></div>

                {/* Second Option */}
                <div className={styles.optionGroup}>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>Secondary Option Name</div>
                      <div className={styles.value}>
                        {companyDetails?.secondOptionName}
                      </div>
                    </div>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Secondary Option Foreign Name
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.secondOptionForeignName}
                      </div>
                    </div>
                  </div>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Clarification for Abbreviation
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.secondClarification}
                      </div>
                    </div>
                  </div>
                </div>

                <div className={styles.divider}></div>

                {/* Third Option */}
                <div className={styles.optionGroup}>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>Tertiary Option Name</div>
                      <div className={styles.value}>
                        {companyDetails?.thirdOptionName}
                      </div>
                    </div>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Tertiary Option Foreign Name
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.thirdOptionForeignName}
                      </div>
                    </div>
                  </div>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Clarification for Abbreviation
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.thirdClarification}
                      </div>
                    </div>
                  </div>
                </div>

                <div className={styles.divider}></div>

                {/* Additional Details */}
                <div className={styles.detailsGroup}>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>Number of Shares</div>
                      <div className={styles.value}>
                        {companyDetails?.numberOfShares}
                      </div>
                    </div>
                    <div className={styles.field}>
                      <div className={styles.label}>Issue Price Per Share</div>
                      <div className={styles.value}>
                        {companyDetails?.issuePricePerShare}
                      </div>
                    </div>
                  </div>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>Source of Initial Fund</div>
                      <div className={styles.value}>
                        {companyDetails?.sourceOfInitialFund}
                      </div>
                    </div>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Relevant Business Activities
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.relevantBusinessActivities}
                      </div>
                    </div>
                  </div>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Labuan License-Trading Activity
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.labuanLicenseTradingActivity}
                      </div>
                    </div>
                  </div>
                  <div className={styles.fullWidthField}>
                    <div className={styles.label}>
                      Describe Nature of Business in Details
                    </div>
                    <div className={styles.value}>
                      {companyDetails?.describeNatureOfBusiness}
                    </div>
                  </div>
                  <div className={styles.fullWidthField}>
                    <div className={styles.label}>
                      Country(ies) in which business is primarily conducted
                    </div>
                    <div className={styles.value}>
                      {companyDetails?.countriesOfBusiness}
                    </div>
                  </div>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Number of Application of Working Permit
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.numberOfWorkingPermit}
                      </div>
                    </div>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Number of Application of Dependent Pass
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.numberOfDependentPass}
                      </div>
                    </div>
                  </div>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Expected Annual Revenue
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.expectedAnnualRevenue}
                      </div>
                    </div>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Value of Asset (current/future)
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.valueOfAsset}
                      </div>
                    </div>
                  </div>
                  <div className={styles.row}>
                    <div className={styles.field}>
                      <div className={styles.label}>
                        Expected number of employee(s)
                      </div>
                      <div className={styles.value}>
                        {companyDetails?.expectedNumberOfEmployees}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Director/Shareholder/UBO Details Section */}
          <div className={styles.section}>
            <div className={styles.title}>
              <h2>Director/Shareholder/UBO Details</h2>
            </div>
            <div className={styles.shareholderWrapper}>
              {shareholderDetails.map((shareholder) => (
                <div key={shareholder.id} className={styles.shareholderCard}>
                  <div className={styles.shareholderContent}>
                    <div className={styles.roleTags}>
                      {shareholder.roles.map((role) => {
                        const tagStyle = getRoleTagColor(role);
                        return (
                          <Tag
                            key={role}
                            style={{
                              background: tagStyle.background,
                              color: tagStyle.color,
                              border: "none",
                              fontSize: "12px",
                              fontFamily: "Inter",
                              fontWeight: "500",
                              lineHeight: "18px",
                              letterSpacing: "0.12px",
                              padding: "2px 12px",
                              borderRadius: "12px",
                            }}
                          >
                            {role}
                          </Tag>
                        );
                      })}
                    </div>
                    <div className={styles.shareholderInfo}>
                      <div className={styles.row}>
                        <div className={styles.field}>
                          <div className={styles.label}>Name</div>
                          <div className={styles.value}>{shareholder.name}</div>
                        </div>
                        <div className={styles.field}>
                          <div className={styles.label}>
                            NRIC/Passport Number
                          </div>
                          <div className={styles.value}>
                            {shareholder.nricPassport}
                          </div>
                        </div>
                      </div>
                      <div className={styles.row}>
                        <div className={styles.field}>
                          <div className={styles.label}>Nationality</div>
                          <div className={styles.value}>
                            {shareholder.nationality}
                          </div>
                        </div>
                        <div className={styles.field}>
                          <div className={styles.label}>
                            Percentage of shares
                          </div>
                          <div className={styles.value}>
                            {shareholder.percentageOfShares}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className={styles.viewMoreSection}>
                    <Button
                      type="default"
                      icon={<DownOutlined />}
                      onClick={() => toggleCardExpansion(shareholder.id)}
                      className={styles.viewMoreButton}
                    >
                      View More
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className={styles.buttonGroup}>
          <Button
            type="default"
            size="large"
            onClick={onBack}
            className={styles.backButton}
          >
            Back
          </Button>
          <Button
            type="primary"
            size="large"
            onClick={handleNext}
            className={styles.nextButton}
          >
            Submit Application
          </Button>
        </div>
      </div>
    </div>
  );
};

export default OverviewStep;
