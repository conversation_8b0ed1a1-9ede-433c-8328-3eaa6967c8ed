import React, { useState } from "react";
import { Button, Input, Checkbox, Form } from "antd";
import type { CompanyNameFormData } from "@/types/applicationForm";
import styles from "./CompanyNameStep.module.less";

const { TextArea } = Input;

interface CompanyNameStepProps {
  onNext: (data: CompanyNameFormData) => void;
  onBack: () => void;
  initialData?: CompanyNameFormData;
}

const CompanyNameStep: React.FC<CompanyNameStepProps> = ({
  onNext,
  onBack,
  initialData,
}) => {
  const [form] = Form.useForm();
  const [formData, setFormData] = useState<CompanyNameFormData>(
    initialData || {
      firstOption: {
        englishName: "",
        hasForeignLanguage: false,
        foreignLanguageName: "",
        hasAbbreviation: false,
        abbreviationClarification: "",
      },
      secondOption: {
        englishName: "",
        hasForeignLanguage: false,
        foreignLanguageName: "",
        hasAbbreviation: false,
        abbreviationClarification: "",
      },
      thirdOption: {
        englishName: "",
        hasForeignLanguage: false,
        foreignLanguageName: "",
        hasAbbreviation: false,
        abbreviationClarification: "",
      },
    }
  );

  const handleFormChange = (
    section: "firstOption" | "secondOption" | "thirdOption",
    field: string,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const handleSubmit = () => {
    form
      .validateFields()
      .then(() => {
        onNext(formData);
      })
      .catch(() => {
        // 表单验证失败
      });
  };

  const renderNameSection = (
    title: string,
    section: "firstOption" | "secondOption" | "thirdOption"
  ) => {
    const data = formData[section];

    return (
      <div className={styles.nameSection}>
        <Form.Item
          name={[section, "englishName"]}
          label={title}
          rules={[
            { required: true, message: `Please enter ${title.toLowerCase()}` },
          ]}
        >
          <Input
            placeholder="Enter Name"
            value={data.englishName}
            onChange={(e) =>
              handleFormChange(section, "englishName", e.target.value)
            }
            className={styles.inputField}
          />
        </Form.Item>

        <div className={styles.checkboxGroup}>
          <Checkbox
            checked={data.hasForeignLanguage}
            onChange={(e) =>
              handleFormChange(section, "hasForeignLanguage", e.target.checked)
            }
          >
            Foreign Language
          </Checkbox>
          <Checkbox
            checked={data.hasAbbreviation}
            onChange={(e) =>
              handleFormChange(section, "hasAbbreviation", e.target.checked)
            }
          >
            Clarification for Abbreviation
          </Checkbox>
        </div>

        {data.hasForeignLanguage && (
          <Form.Item
            name={[section, "foreignLanguageName"]}
            label={`Foreign Language ${title}`}
            rules={
              data.hasForeignLanguage
                ? [
                    {
                      required: true,
                      message: "Please enter foreign language name",
                    },
                  ]
                : []
            }
          >
            <Input
              placeholder="Enter Name"
              value={data.foreignLanguageName}
              onChange={(e) =>
                handleFormChange(section, "foreignLanguageName", e.target.value)
              }
              className={styles.inputField}
            />
          </Form.Item>
        )}

        {data.hasAbbreviation && (
          <Form.Item
            name={[section, "abbreviationClarification"]}
            label="Clarification for Abbreviation"
            rules={
              data.hasAbbreviation
                ? [
                    {
                      required: true,
                      message: "Please enter abbreviation clarification",
                    },
                  ]
                : []
            }
          >
            <TextArea
              placeholder="Enter Clarification"
              value={data.abbreviationClarification}
              onChange={(e) =>
                handleFormChange(
                  section,
                  "abbreviationClarification",
                  e.target.value
                )
              }
              className={styles.textareaField}
              rows={6}
            />
          </Form.Item>
        )}
      </div>
    );
  };

  return (
    <div className={styles.companyNameStep}>
      <div className={styles.container}>
        <div className={styles.header}>
          <h1 className={styles.title}>Proposed Company Name</h1>
          <p className={styles.subtitle}>
            Note: The proposed name in a foreign language will follow the
            proposed name in English on the company's certificate of
            incorporation.
          </p>
        </div>

        <div className={styles.formCard}>
          <Form
            form={form}
            layout="vertical"
            initialValues={formData}
            className={styles.form}
          >
            {renderNameSection("First Option (English)", "firstOption")}
            {renderNameSection("Secondary Option (English)", "secondOption")}
            {renderNameSection("Tertiary Option (English)", "thirdOption")}
          </Form>
        </div>

        <div className={styles.buttonGroup}>
          <Button
            type="default"
            size="large"
            onClick={onBack}
            className={styles.backButton}
          >
            Back
          </Button>
          <Button
            type="primary"
            size="large"
            onClick={handleSubmit}
            className={styles.nextButton}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CompanyNameStep;
