import React from 'react';
import { Form, Radio, Space } from 'antd';

interface PEPStatusSelectorProps {
  name?: string;
  label?: string;
  entityType?: 'individual' | 'corporate';
}

const PEPStatusSelector: React.FC<PEPStatusSelectorProps> = ({
  name = 'isPEP',
  label,
  entityType = 'individual',
}) => {
  const defaultLabel = entityType === 'individual'
    ? 'Has this person represented or previously represented any Political Organization or individuals who are Politically Exposed Persons (PEPs)?'
    : 'Has this entity or its authorized representative represented or previously represented any Political Organization or individuals who are Politically Exposed Persons (PEPs)?';

  return (
    <Form.Item
      label={label || defaultLabel}
      name={name}
      rules={[{ required: true, message: 'Please select an option' }]}
    >
      <Radio.Group>
        <Space size="large">
          <Radio value={true}>Yes</Radio>
          <Radio value={false}>No</Radio>
        </Space>
      </Radio.Group>
    </Form.Item>
  );
};

export default PEPStatusSelector;
