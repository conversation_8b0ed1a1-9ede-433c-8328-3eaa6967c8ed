import React, { useState, useEffect } from "react";
import { CheckCircleFilled } from "@ant-design/icons";
import styles from "./SubmitStep.module.less";

interface SubmitStepProps {
  onNext: () => void;
  onBack: () => void;
}

const SubmitStep: React.FC<SubmitStepProps> = ({ onNext, onBack }) => {
  const [isSubmitted, setIsSubmitted] = useState(false);

  useEffect(() => {
    // 模拟提交过程
    const submitForm = async () => {
      // 这里可以添加实际的提交逻辑
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setIsSubmitted(true);
    };

    submitForm();
  }, []);

  return (
    <div className={styles.submitStep}>
      <div className={styles.container}>
        <div className={styles.successContent}>
          <div className={styles.iconWrapper}>
            <div className={styles.successCircle}>
              <CheckCircleFilled className={styles.checkIcon} />
            </div>
          </div>

          <div className={styles.textContent}>
            <h1 className={styles.title}>
              Registration form submitted successfully
            </h1>
            <p className={styles.description}>
              We are reviewing the information you submitted and will notify you
              as soon as the review is completed.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubmitStep;
