.shareholderDetailStep {
  min-height: 100vh;
  background: #fcfcfe;
  padding: 0;

  .container {
    width: 804px;
    margin: 0 auto;
    padding: 78px 0;
  }

  .header {
    margin-bottom: 48px;

    .backButton {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 2px;
      border-radius: 10px;
      color: #344054;
      font-size: 14px;
      font-weight: 600;
      line-height: 18px;
      letter-spacing: 0.14px;
      margin-bottom: 24px;

      &:hover {
        border-color: transparent;
        color: #1b3770;
        background: transparent;
      }
    }

    .title {
      color: #081021;
      font-size: 24px;
      font-weight: 600;
      line-height: 32px;
      letter-spacing: 0.24px;
      margin: 0;
    }
  }

  .formContainer {
    background: white;
    border-radius: 12px;
    border: 1px solid #e4e7ec;
    padding: 24px;

    .form {
      .ant-form-item-label > label {
        color: #081021;
        font-size: 14px;
        font-weight: 500;
        line-height: 18px;
        letter-spacing: 0.14px;
      }

      .ant-input,
      .ant-select-selector,
      .ant-input-number {
        padding: 12px;
        border-radius: 8px;
        border: 1px solid #d0d5dd;
        box-shadow: 0px 0.5px 1px rgba(24, 32, 60, 0.05);
        font-size: 16px;
        line-height: 20px;
        letter-spacing: 0.16px;

        &:focus,
        &:hover {
          border-color: #1b3770;
          box-shadow: 0px 0px 0px 4px rgba(27, 55, 112, 0.1);
        }
      }

      .ant-input-number {
        width: 100%;
      }

      .ant-select-selector {
        padding: 0 12px !important;
        height: 44px !important;
        display: flex;
        align-items: center;
      }

      .ant-select-selection-item {
        line-height: 20px !important;
      }

      .ant-input-affix-wrapper {
        padding: 12px;
        border-radius: 8px;
        border: 1px solid #d0d5dd;
        box-shadow: 0px 0.5px 1px rgba(24, 32, 60, 0.05);

        .ant-input {
          padding: 0;
          border: none;
          box-shadow: none;
        }

        &:focus,
        &:hover {
          border-color: #1b3770;
          box-shadow: 0px 0px 0px 4px rgba(27, 55, 112, 0.1);
        }
      }

      .ant-input-suffix {
        color: #667085;
        font-size: 16px;
        font-weight: 400;
      }

      .checkboxCard {
        min-width: 189px;
        padding: 20px 16px;
        background: white;
        border-radius: 8px;
        border: 1px solid #d0d5dd;
        display: flex;
        align-items: center;
        gap: 12px;

        .ant-checkbox-wrapper {
          display: flex;
          align-items: center;
          gap: 12px;
          margin: 0;

          .ant-checkbox {
            .ant-checkbox-inner {
              width: 16px;
              height: 16px;
              border-radius: 4px;
              border-color: #d0d5dd;
            }

            &.ant-checkbox-checked {
              .ant-checkbox-inner {
                background-color: #1b3770;
                border-color: #1b3770;
              }
            }
          }

          span:last-child {
            color: #081021;
            font-size: 16px;
            font-weight: 400;
            line-height: 20px;
            letter-spacing: 0.16px;
          }
        }

        &:hover {
          border-color: #1b3770;
        }
      }

      .ant-radio-group {
        .ant-radio-wrapper {
          color: #667085;
          font-size: 16px;
          font-weight: 400;
          line-height: 20px;
          letter-spacing: 0.16px;

          .ant-radio {
            .ant-radio-inner {
              width: 16px;
              height: 16px;
              border-color: #d0d5dd;
            }

            &.ant-radio-checked {
              .ant-radio-inner {
                border-color: #1b3770;

                &::after {
                  background-color: #1b3770;
                }
              }
            }
          }
        }
      }

      .ant-divider {
        margin: 32px 0 24px;
        border-color: #e4e7ec;

        .ant-divider-inner-text {
          color: #081021;
          font-size: 16px;
          font-weight: 600;
          line-height: 20px;
          letter-spacing: 0.16px;
        }
      }
    }

    .submitSection {
      margin-top: 32px;
      margin-bottom: 0;

      .submitButton {
        width: 100%;
        height: 44px;
        background: #204184;
        border-radius: 10px;
        border: none;
        box-shadow: 0px 3px 4px rgba(223, 237, 255, 0.1) inset;
        color: white;
        font-size: 16px;
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 0.16px;

        &:hover {
          background: #1b3770;
        }

        &:focus {
          background: #1b3770;
          box-shadow: 0px 0px 0px 4px rgba(27, 55, 112, 0.1);
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .shareholderDetailStep {
    .formContainer {
      padding: 16px;

      .form {
        .ant-row {
          .ant-col {
            margin-bottom: 16px;
          }
        }

        .checkboxCard {
          min-width: auto;
          width: 100%;
          margin-bottom: 12px;
        }
      }
    }
  }
}
