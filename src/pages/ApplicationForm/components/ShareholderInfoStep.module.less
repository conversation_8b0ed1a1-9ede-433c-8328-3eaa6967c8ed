// ShareholderInfoStep 组件样式
.shareholderInfoStep {
  width: 100%;
  min-height: calc(100vh - 93px);
  background: #fcfcfe;
  display: flex;
  justify-content: center;
  padding: 40px 20px;

  .container {
    width: 1280px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;

    .loadingContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;
      min-height: 400px;
      color: var(--Text-text-tertiary-(600), #475467);
      font-size: 16px;
      font-family: "Inter", sans-serif;
    }

    .header {
      display: flex;
      flex-direction: column;
      gap: 20px;

      .title {
        color: var(--Text-text-primary-(900), #081021);
        font-size: 24px;
        font-family: "Inter", sans-serif;
        font-weight: 600;
        line-height: 32px;
        letter-spacing: 0.24px;
        margin: 0;
      }
    }

    .shareholderGrid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(405px, 1fr));
      gap: 32px;
      justify-items: center;

      .shareholderCard {
        width: 405px;
        height: 526px;
        padding: 24px 20px;
        position: relative;
        background: var(--Background-bg-primary, white);
        border-radius: 12px;
        border: 1px solid var(--Border-border-secondary, #e4e7ec);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;

        .deleteButton {
          position: absolute;
          top: 14px;
          right: 14px;
          padding: 4px;
          border-radius: 6px;
          color: var(--Foreground-fg-tertiary--600-, #475467);

          &:hover {
            background: #f5f5f5;
          }
        }

        .processingContent {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 12px;
          height: 100%;

          .processingText {
            color: var(--Text-text-quaternary-(500), #667085);
            font-size: 16px;
            font-family: "Inter", sans-serif;
            font-weight: 500;
            line-height: 20px;
            letter-spacing: 0.16px;
          }
        }

        .errorContent {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 12px;
          height: 100%;

          .fileName {
            color: var(--Text-text-quaternary-(500), #667085);
            font-size: 16px;
            font-family: "Inter", sans-serif;
            font-weight: 500;
            line-height: 20px;
            letter-spacing: 0.16px;
          }

          .errorMessage {
            color: var(--Text-text-error-primary-(600), #d92d20);
            font-size: 14px;
            font-family: "Inter", sans-serif;
            font-weight: 400;
            line-height: 18px;
            letter-spacing: 0.14px;
          }
        }

        .cardContent {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 24px;
          width: 100%;

          .avatarSection {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;

            .avatar {
              background: var(--Brand-Blue-50, #dee3ed);
              border: 0.75px solid rgba(0, 0, 0, 0.08);

              :global(.ant-avatar-icon) {
                color: var(--Brand-Blue-500, #204184);
                font-size: 24px;
              }
            }
          }

          .tags {
            display: flex;
            gap: 16px;

            .shareholderTag {
              background: #b1d5ff;
              color: #003675;
              border: none;
              font-size: 12px;
              font-family: "Inter", sans-serif;
              font-weight: 500;
              line-height: 18px;
              letter-spacing: 0.12px;
              padding: 2px 12px;
              border-radius: 12px;
            }

            .uboTag {
              background: #ffe9d3;
              color: #6f3800;
              border: none;
              font-size: 12px;
              font-family: "Inter", sans-serif;
              font-weight: 500;
              line-height: 18px;
              letter-spacing: 0.12px;
              padding: 2px 12px;
              border-radius: 12px;
            }
          }

          .infoSection {
            display: flex;
            flex-direction: column;
            gap: 12px;
            width: 100%;

            .infoItem {
              display: flex;
              flex-direction: column;
              gap: 4px;

              .label {
                display: flex;
                align-items: center;
                gap: 8px;
                color: var(--Text-text-tertiary-(600), #475467);
                font-size: 14px;
                font-family: "Inter", sans-serif;
                font-weight: 400;
                line-height: 18px;
                letter-spacing: 0.14px;

                .editIcon {
                  color: var(--Foreground-fg-quaternary--500-, #667085);
                  font-size: 14px;
                  cursor: pointer;

                  &:hover {
                    color: var(--Brand-Blue-500, #204184);
                  }
                }
              }

              .value {
                color: var(--Text-text-primary-(900), #081021);
                font-size: 14px;
                font-family: "Inter", sans-serif;
                font-weight: 500;
                line-height: 18px;
                letter-spacing: 0.14px;
              }
            }
          }
        }

        .buttonSection {
          display: flex;
          flex-direction: column;
          gap: 4px;
          width: 100%;

          .completionText {
            text-align: center;
            color: var(--Text-text-success-primary-(600), #079455);
            font-size: 12px;
            font-family: "Inter", sans-serif;
            font-weight: 500;
            line-height: 18px;
            letter-spacing: 0.12px;
          }

          .actionButton {
            width: 100%;
            height: 48px;
            border-radius: 10px;
            font-size: 16px;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            line-height: 20px;
            letter-spacing: 0.16px;

            &:global(.ant-btn-primary) {
              background: var(--Button-button-primary-bg, #204184);
              border: none;
              color: var(--Button-button-primary-fg, white);
              box-shadow: 0px 3px 4px rgba(223.02, 237.74, 255, 0.1) inset;

              &:hover {
                background: #1a3670 !important;
              }
            }

            &:global(.ant-btn-default) {
              background: var(--Button-button-secondary-color-bg, white);
              border: 1px solid
                var(--Button-button-secondary-color-border, #5871a3);
              color: var(--Button-button-secondary-color-fg, #1b3770);

              &:hover {
                background: #f8f9fb !important;
                border-color: #4a6491 !important;
                color: #1b3770 !important;
              }
            }
          }
        }
      }
    }

    .buttonGroup {
      display: flex;
      justify-content: space-between;
      gap: 20px;
      margin-top: 40px;

      .backButton,
      .nextButton {
        width: 300px;
        height: 48px;
        border-radius: 10px;
        font-size: 16px;
        font-family: "Inter", sans-serif;
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 0.16px;
      }

      .backButton {
        background: var(--Button-button-secondary-color-bg, white);
        border: 1px solid var(--Button-button-secondary-color-border, #5871a3);
        color: var(--Button-button-secondary-color-fg, #1b3770);

        &:hover {
          background: #f8f9fb !important;
          border-color: #4a6491 !important;
          color: #1b3770 !important;
        }
      }

      .nextButton {
        background: var(--Button-button-primary-bg, #204184);
        border: none;
        color: var(--Button-button-primary-fg, white);
        box-shadow: 0px 3px 4px rgba(223.02, 237.74, 255, 0.1) inset;

        &:hover {
          background: #1a3670 !important;
        }

        &:focus {
          background: #1a3670 !important;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1320px) {
  .shareholderInfoStep {
    .container {
      width: 100%;
      padding: 0 20px;

      .shareholderGrid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(405px, 1fr));
        gap: 32px;
        justify-items: center;
      }
    }
  }
}

@media (max-width: 768px) {
  .shareholderInfoStep {
    padding: 20px 16px;

    .container {
      padding: 0;

      .header {
        .title {
          font-size: 20px;
          line-height: 28px;
        }
      }

      .shareholderGrid {
        grid-template-columns: 1fr;
        gap: 24px;

        .shareholderCard {
          width: 100%;
          max-width: 405px;
          height: auto;
          min-height: 526px;
        }
      }

      .buttonGroup {
        flex-direction: column;
        margin-top: 32px;

        .backButton,
        .nextButton {
          width: 100%;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .shareholderInfoStep {
    padding: 16px 12px;

    .container {
      .header {
        .title {
          font-size: 18px;
          line-height: 24px;
        }
      }

      .shareholderGrid {
        .shareholderCard {
          padding: 20px 16px;

          .cardContent {
            gap: 20px;

            .infoSection {
              gap: 10px;

              .infoItem {
                .label {
                  font-size: 12px;
                }

                .value {
                  font-size: 12px;
                }
              }
            }
          }

          .buttonSection {
            .actionButton {
              font-size: 14px;
              height: 44px;
            }
          }
        }
      }

      .buttonGroup {
        .backButton,
        .nextButton {
          font-size: 14px;
          height: 44px;
        }
      }
    }
  }
}
