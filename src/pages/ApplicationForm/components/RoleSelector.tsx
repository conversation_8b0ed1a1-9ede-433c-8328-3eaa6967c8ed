import React from 'react';
import { Form, Checkbox, Space } from 'antd';
import styles from './ShareholderDetailStep.module.less';

interface RoleSelectorProps {
  name?: string;
  showUBO?: boolean;
}

const RoleSelector: React.FC<RoleSelectorProps> = ({
  name = 'roles',
  showUBO = true,
}) => {
  return (
    <Form.Item label="Role in the Company" name={name}>
      <Checkbox.Group>
        <Space direction="horizontal" size="large">
          <div className={styles.checkboxCard}>
            <Checkbox value="director">Director</Checkbox>
          </div>
          <div className={styles.checkboxCard}>
            <Checkbox value="shareholder">Shareholder</Checkbox>
          </div>
          {showUBO && (
            <div className={styles.checkboxCard}>
              <Checkbox value="ubo">UBO</Checkbox>
            </div>
          )}
        </Space>
      </Checkbox.Group>
    </Form.Item>
  );
};

export default RoleSelector;
