import React from 'react';
import styles from './ChecklistItem.module.less';

interface ChecklistItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const ChecklistItem: React.FC<ChecklistItemProps> = ({ icon, title, description }) => {
  return (
    <div className={styles.checklistItem}>
      <div className={styles.icon}>
        {icon}
      </div>
      <div className={styles.content}>
        <div className={styles.title}>{title}</div>
        <div className={styles.description}>{description}</div>
      </div>
    </div>
  );
};

export default ChecklistItem;
