.documentUploadStep {
  width: 100%;
  min-height: calc(100vh - 93px);
  background: #fcfcfe;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;

  .container {
    width: 752px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 32px;

    .content {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 32px;

      .header {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .title {
          color: var(--Text-text-primary-(900), #081021);
          font-size: 18px;
          font-family: "Inter", sans-serif;
          font-weight: 500;
          line-height: 24px;
          letter-spacing: 0.18px;
          margin: 0;
          text-align: left;
        }
      }

      .instructions {
        display: flex;
        flex-direction: column;
        gap: 16px;

        .instructionCard {
          padding: 12px;
          background: #e7efff;
          border-radius: 8px;
          border: none;

          :global(.ant-alert-message) {
            color: var(--Brand-Blue-500, #204184);
            font-size: 14px;
            font-family: "Inter", sans-serif;
            font-weight: 600;
            line-height: 18px;
            letter-spacing: 0.14px;
            margin-bottom: 4px;
          }

          :global(.ant-alert-description) {
            color: var(--Brand-Blue-500, #204184);
            font-size: 12px;
            font-family: "Inter", sans-serif;
            font-weight: 400;
            line-height: 18px;
            letter-spacing: 0.12px;
          }

          .instructionList {
            display: flex;
            flex-direction: column;
            gap: 4px;

            .instructionItem {
              color: var(--Brand-Blue-500, #204184);
              font-size: 12px;
              font-family: "Inter", sans-serif;
              font-weight: 400;
              line-height: 18px;
              letter-spacing: 0.12px;

              .bold {
                font-weight: 500;
              }
            }
          }
        }
      }

      .uploadSection {
        width: 100%;

        .uploadArea {
          width: 100%;
          height: 200px;
          padding: 16px 24px;
          background: var(--Background-bg-primary, white);
          border-radius: 12px;
          border: 1.5px dashed var(--Border-border-primary, #d0d5dd);
          display: block;

          :global(.ant-upload-drag) {
            background: transparent !important;
            border: none !important;
            height: 100% !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
          }

          :global(.ant-upload-drag-container) {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .uploadContent {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 12px;
            width: 100%;

            .uploadIcon {
              width: 40px;
              height: 40px;
              background: var(--Background-bg-primary, white);
              border-radius: 8px;
              border: 1px solid var(--Border-border-primary, #d0d5dd);
              display: flex;
              align-items: center;
              justify-content: center;

              :global(.anticon) {
                font-size: 20px;
                color: var(--Colors-Foreground-fg-secondary--700-, #cecfd2);
              }
            }

            .uploadText {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 4px;
              width: 100%;

              .uploadAction {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 4px;

                .clickText {
                  color: var(--Foreground-fg-secondary-(700), #344054);
                  font-size: 14px;
                  font-family: "Inter", sans-serif;
                  font-weight: 600;
                  line-height: 18px;
                  letter-spacing: 0.14px;
                  text-decoration: underline;
                  cursor: pointer;
                }

                .dragText {
                  color: var(--Text-text-tertiary-(600), #475467);
                  font-size: 14px;
                  font-family: "Inter", sans-serif;
                  font-weight: 400;
                  line-height: 18px;
                  letter-spacing: 0.14px;
                }
              }

              .supportText {
                text-align: center;
                color: var(--Text-text-tertiary-(600), #475467);
                font-size: 12px;
                font-family: "Inter", sans-serif;
                font-weight: 400;
                line-height: 18px;
                letter-spacing: 0.12px;
              }
            }
          }

          &:hover {
            border-color: var(--Background-bg-brand-solid, #1b3770);
          }
        }
      }

      .fileList {
        width: 100%;
        padding: 16px;
        background: var(--Background-bg-primary, white);
        border-radius: 8px;
        border: 1px solid var(--Border-border-secondary, #e4e7ec);

        .fileListTitle {
          color: var(--Text-text-primary-(900), #081021);
          font-size: 14px;
          font-family: "Inter", sans-serif;
          font-weight: 600;
          line-height: 18px;
          margin: 0 0 12px 0;
        }

        .files {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .fileItem {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            background: #f8f9fb;
            border-radius: 6px;

            .fileName {
              color: var(--Text-text-primary-(900), #081021);
              font-size: 14px;
              font-family: "Inter", sans-serif;
              font-weight: 400;
              line-height: 18px;
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .fileStatus {
              font-size: 12px;
              font-family: "Inter", sans-serif;
              font-weight: 500;

              .uploading {
                color: #faad14;
              }

              .success {
                color: #52c41a;
              }

              .error {
                color: #ff4d4f;
              }
            }
          }
        }
      }
    }
  }

  .shareholderGridContainer {
    width: 1280px;
    margin: 0 auto;
    .shareholderGridTitle {
      color: var(--Text-text-primary-(900), #081021);
      font-size: 24px;
      font-family: "Inter", sans-serif;
      font-weight: 600;
      line-height: 32px;
      letter-spacing: 0.24px;
      margin: 0;
    }

    .shareholderGrid {
      display: grid;
      grid-template-columns: repeat(3, minmax(405px, 1fr));
      gap: 32px;
      margin-top: 20px;

      .shareholderCard {
        width: 405px;
        height: 526px;
        padding: 24px 20px;
        position: relative;
        background: var(--Background-bg-primary, white);
        border-radius: 12px;
        border: 1px solid var(--Border-border-secondary, #e4e7ec);
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;

        .deleteButton {
          position: absolute;
          top: 14px;
          right: 14px;
          padding: 4px;
          border-radius: 6px;
          border: none;
          background: transparent;
          color: var(--Text-text-tertiary-(500), #667085);
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background: rgba(102, 112, 133, 0.1);
            color: var(--Text-text-secondary-(600), #344054);
          }
        }

        .processingContent {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 16px;
          justify-content: center;
          height: 100%;

          .processingText {
            color: var(--Text-text-tertiary-(500), #667085);
            font-size: 16px;
            font-family: "Inter", sans-serif;
            font-weight: 400;
            line-height: 20px;
            text-align: center;
          }
        }

        .errorContent {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 12px;
          justify-content: center;
          height: 100%;

          .fileName {
            color: var(--Text-text-primary-(900), #081021);
            font-size: 16px;
            font-family: "Inter", sans-serif;
            font-weight: 500;
            line-height: 20px;
            text-align: center;
          }

          .errorMessage {
            color: #f04438;
            font-size: 14px;
            font-family: "Inter", sans-serif;
            font-weight: 400;
            line-height: 18px;
            text-align: center;
          }
        }

        .cardContent {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 24px;
          width: 100%;

          .avatarSection {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;

            .avatar {
              background: var(--Brand-Blue-50, #dee3ed);
              border: 0.75px solid rgba(0, 0, 0, 0.08);

              :global(.ant-avatar-icon) {
                color: var(--Brand-Blue-500, #204184);
                font-size: 24px;
              }
            }
          }

          .tags {
            display: flex;
            gap: 16px;

            .fileTag {
              background: #d1fadf;
              color: var(--Text-text-success-primary-(600), #079455);
              border: none;
              font-size: 12px;
              font-family: "Inter", sans-serif;
              font-weight: 500;
              line-height: 18px;
              letter-spacing: 0.12px;
              padding: 2px 12px;
              border-radius: 12px;
            }

            .statusTag {
              background: #b1d5ff;
              color: var(--Brand-Blue-500, #204184);
              border: none;
              font-size: 12px;
              font-family: "Inter", sans-serif;
              font-weight: 500;
              line-height: 18px;
              letter-spacing: 0.12px;
              padding: 2px 12px;
              border-radius: 12px;
            }
          }

          .infoSection {
            display: flex;
            flex-direction: column;
            gap: 12px;
            width: 100%;

            .infoItem {
              display: flex;
              flex-direction: column;
              gap: 4px;

              .label {
                color: var(--Text-text-tertiary-(500), #667085);
                font-size: 14px;
                font-family: "Inter", sans-serif;
                font-weight: 400;
                line-height: 20px;
                letter-spacing: 0.14px;
                display: flex;
                align-items: center;
                gap: 4px;

                .editIcon {
                  color: var(--Text-text-tertiary-(500), #667085);
                  font-size: 12px;
                  cursor: pointer;

                  &:hover {
                    color: var(--Text-text-secondary-(600), #344054);
                  }
                }
              }

              .value {
                color: var(--Text-text-primary-(900), #081021);
                font-size: 16px;
                font-family: "Inter", sans-serif;
                font-weight: 500;
                line-height: 20px;
                letter-spacing: 0.16px;
              }
            }
          }
        }

        .buttonSection {
          display: flex;
          flex-direction: column;
          gap: 4px;
          width: 100%;

          .completionText {
            text-align: center;
            color: var(--Text-text-success-primary-(600), #079455);
            font-size: 12px;
            font-family: "Inter", sans-serif;
            font-weight: 500;
            line-height: 18px;
            letter-spacing: 0.12px;
          }

          .actionButton {
            width: 100%;
            height: 48px;
            border-radius: 10px;
            font-size: 16px;
            font-family: "Inter", sans-serif;
            font-weight: 500;
            line-height: 20px;
            letter-spacing: 0.16px;
            &:global(.ant-btn-primary) {
              background: var(--Button-button-primary-bg, #204184);
            }
          }
        }
      }
    }
    .buttonGroup {
      width: 100%;
      display: flex;
      justify-content: space-between;
      gap: 20px;
      margin-top: 64px;

      .backButton,
      .nextButton {
        width: 300px;
        height: 48px;
        border-radius: 10px;
        font-size: 16px;
        font-family: "Inter", sans-serif;
        font-weight: 600;
        line-height: 20px;
        letter-spacing: 0.16px;
      }

      .backButton {
        background: var(--Button-button-secondary-color-bg, white);
        border: 1px solid var(--Button-button-secondary-color-border, #5871a3);
        color: var(--Button-button-secondary-color-fg, #1b3770);

        &:hover {
          background: #f8f9fb !important;
          border-color: #4a6491 !important;
          color: #1b3770 !important;
        }
      }

      .nextButton {
        background: var(--Button-button-primary-bg, #204184);
        border: none;
        color: var(--Button-button-primary-fg, white);
        box-shadow: 0px 3px 4px rgba(223.02, 237.74, 255, 0.1) inset;

        &:hover {
          background: #1a3670 !important;
        }

        &:focus {
          background: #1a3670 !important;
        }

        &:global(.ant-btn-loading) {
          background: #1a3670 !important;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .documentUploadStep {
    padding: 20px 16px;

    .container {
      width: 100%;
      gap: 24px;

      .content {
        gap: 24px;

        .header {
          .title {
            font-size: 16px;
            line-height: 22px;
          }
        }

        .uploadSection {
          .uploadArea {
            height: 160px;
            padding: 12px 16px;

            .uploadContent {
              gap: 8px;

              .uploadIcon {
                width: 32px;
                height: 32px;

                :global(.anticon) {
                  font-size: 16px;
                }
              }

              .uploadText {
                .uploadAction {
                  flex-direction: column;
                  gap: 2px;

                  .clickText,
                  .dragText {
                    font-size: 12px;
                  }
                }

                .supportText {
                  font-size: 10px;
                }
              }
            }
          }
        }
      }
    }

    .buttonGroup {
      flex-direction: column;

      .backButton,
      .nextButton {
        width: 100%;
      }
    }
  }
}

@media (max-width: 480px) {
  .documentUploadStep {
    padding: 16px 12px;

    .container {
      .content {
        .instructions {
          .instructionCard {
            padding: 8px;

            :global(.ant-alert-message) {
              font-size: 12px;
            }

            :global(.ant-alert-description) {
              font-size: 10px;
            }
          }
        }
      }
    }

    .buttonGroup {
      .backButton,
      .nextButton {
        font-size: 14px;
        height: 44px;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .shareholderGrid {
      grid-template-columns: 1fr;
      gap: 24px;

      .shareholderCard {
        width: 100%;
        max-width: 405px;
      }
    }
  }

  @media (max-width: 480px) {
    .shareholderGrid {
      .shareholderCard {
        padding: 20px 16px;
        min-height: 180px;
      }
    }
  }
}
