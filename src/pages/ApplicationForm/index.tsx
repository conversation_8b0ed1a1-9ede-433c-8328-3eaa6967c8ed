import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Spin, message } from "antd";
import { ApplicationHeader } from "@/components";
import {
  ApplicationStep,
  type CompanyNameFormData,
  type CompanyDetailsFormData,
  type DocumentUploadFormData,
  type ShareholderInfoFormData,
  type IndividualShareholderFormData,
  type CorporateShareholderFormData,
  ShareholderType,
} from "@/types/applicationForm";
import {
  getApplicationForm,
  saveApplicationForm,
} from "@/services/applicationForm";
import StartStep from "./components/StartStep";
import CompanyNameStep from "./components/CompanyNameStep";
import CompanyDetailsStep from "./components/CompanyDetailsStep";
import DocumentUploadStep from "./components/DocumentUploadStep";
import ShareholderInfoStep from "./components/ShareholderInfoStep";
import IndividualShareholderStep from "./components/IndividualShareholderStep";
import CorporateShareholderStep from "./components/CorporateShareholderStep";
import OverviewStep from "./components/OverviewStep";
import SubmitStep from "./components/SubmitStep";
import styles from "./index.module.less";

const ApplicationForm: React.FC = () => {
  const { applicationId } = useParams<{ applicationId: string }>();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState<ApplicationStep>(
    ApplicationStep.START
  );
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState<{
    companyName?: CompanyNameFormData;
    companyDetails?: CompanyDetailsFormData;
    documentUpload?: DocumentUploadFormData;
    shareholderInfo?: ShareholderInfoFormData;
  }>({});
  const [currentShareholderId, setCurrentShareholderId] = useState<
    string | null
  >(null);
  const [currentShareholderType, setCurrentShareholderType] =
    useState<ShareholderType | null>(null);

  useEffect(() => {
    initializeForm();
  }, [applicationId]);

  const initializeForm = async () => {
    try {
      setLoading(true);
      const response = await getApplicationForm(applicationId);
      if (response.success && response.data) {
        // setCurrentStep(response.data.currentStep);
        setCurrentStep(ApplicationStep.DOCUMENT_UPLOAD);
      }
    } catch (error) {
      message.error("Failed to load application form");
    } finally {
      setLoading(false);
    }
  };

  const handleStepChange = async (step: ApplicationStep) => {
    try {
      const response = await saveApplicationForm({ currentStep: step });
      if (response.success) {
        setCurrentStep(step);
      } else {
        message.error("Failed to save progress");
      }
    } catch (error) {
      message.error("Failed to save progress");
    }
  };

  const handleNext = () => {
    switch (currentStep) {
      case ApplicationStep.START:
        handleStepChange(ApplicationStep.COMPANY_NAME);
        break;
      case ApplicationStep.COMPANY_NAME:
        handleStepChange(ApplicationStep.COMPANY_DETAILS);
        break;
      case ApplicationStep.COMPANY_DETAILS:
        handleStepChange(ApplicationStep.DOCUMENT_UPLOAD);
        break;
      case ApplicationStep.DOCUMENT_UPLOAD:
        handleStepChange(ApplicationStep.OVERVIEW);
        break;
      case ApplicationStep.OVERVIEW:
        handleStepChange(ApplicationStep.SUBMIT);
        break;
      default:
        break;
    }
  };

  const handleBack = () => {
    switch (currentStep) {
      case ApplicationStep.COMPANY_NAME:
        handleStepChange(ApplicationStep.START);
        break;
      case ApplicationStep.COMPANY_DETAILS:
        handleStepChange(ApplicationStep.COMPANY_NAME);
        break;
      case ApplicationStep.DOCUMENT_UPLOAD:
        handleStepChange(ApplicationStep.COMPANY_DETAILS);
        break;
      case ApplicationStep.OVERVIEW:
        handleStepChange(ApplicationStep.DOCUMENT_UPLOAD);
        break;
      case ApplicationStep.SUBMIT:
        handleStepChange(ApplicationStep.OVERVIEW);
        break;
      default:
        break;
    }
  };

  const handleCompanyNameSubmit = (data: CompanyNameFormData) => {
    setFormData((prev) => ({ ...prev, companyName: data }));
    handleNext();
  };

  const handleCompanyDetailsSubmit = (data: CompanyDetailsFormData) => {
    setFormData((prev) => ({ ...prev, companyDetails: data }));
    handleNext();
  };

  const handleDocumentUploadSubmit = (data: DocumentUploadFormData) => {
    setFormData((prev) => ({ ...prev, documentUpload: data }));
    handleNext();
  };

  const handleShareholderInfoSubmit = (data: ShareholderInfoFormData) => {
    setFormData((prev) => ({ ...prev, shareholderInfo: data }));
    handleNext();
  };

  const handleEditShareholder = (
    shareholderId: string,
    type: ShareholderType
  ) => {
    // 设置当前编辑的股东信息
    setCurrentShareholderId(shareholderId);
    setCurrentShareholderType(type);

    // 根据股东类型导航到相应的详情页面
    if (type === ShareholderType.INDIVIDUAL) {
      handleStepChange(ApplicationStep.INDIVIDUAL_SHAREHOLDER);
    } else {
      handleStepChange(ApplicationStep.CORPORATE_SHAREHOLDER);
    }
  };

  const handleIndividualShareholderSubmit = async (
    data: IndividualShareholderFormData
  ) => {
    try {
      // 保存个人股东数据
      console.log("Individual shareholder data:", data);
      message.success("Individual shareholder details saved successfully");
      // 返回文档上传页面
      handleStepChange(ApplicationStep.DOCUMENT_UPLOAD);
    } catch (error) {
      message.error("Failed to save individual shareholder details");
    }
  };

  const handleCorporateShareholderSubmit = async (
    data: CorporateShareholderFormData
  ) => {
    try {
      // 保存公司股东数据
      console.log("Corporate shareholder data:", data);
      message.success("Corporate shareholder details saved successfully");
      // 返回文档上传页面
      handleStepChange(ApplicationStep.DOCUMENT_UPLOAD);
    } catch (error) {
      message.error("Failed to save corporate shareholder details");
    }
  };

  const handleOverviewSubmit = () => {
    handleNext();
  };

  const handleSubmitSubmit = () => {
    // 提交完成后可以导航到其他页面或显示成功消息
    message.success("Application submitted successfully!");
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case ApplicationStep.START:
        return <StartStep onNext={handleNext} />;
      case ApplicationStep.COMPANY_NAME:
        return (
          <CompanyNameStep
            onNext={handleCompanyNameSubmit}
            onBack={handleBack}
            initialData={formData.companyName}
          />
        );
      case ApplicationStep.COMPANY_DETAILS:
        return (
          <CompanyDetailsStep
            onNext={handleCompanyDetailsSubmit}
            onBack={handleBack}
            initialData={formData.companyDetails}
          />
        );
      case ApplicationStep.DOCUMENT_UPLOAD:
        return (
          <DocumentUploadStep
            onNext={handleDocumentUploadSubmit}
            onBack={handleBack}
            goStep={(step: ApplicationStep) => handleStepChange(step)}
            initialData={formData.documentUpload}
          />
        );
      case ApplicationStep.INDIVIDUAL_SHAREHOLDER:
        return (
          <IndividualShareholderStep
            onNext={handleIndividualShareholderSubmit}
            onBack={() => handleStepChange(ApplicationStep.DOCUMENT_UPLOAD)}
            shareholderName="TAN WEI MING"
          />
        );
      case ApplicationStep.CORPORATE_SHAREHOLDER:
        return (
          <CorporateShareholderStep
            onNext={handleCorporateShareholderSubmit}
            onBack={() => handleStepChange(ApplicationStep.DOCUMENT_UPLOAD)}
            shareholderName="ABC Company"
          />
        );
      case ApplicationStep.OVERVIEW:
        return (
          <OverviewStep
            onNext={handleOverviewSubmit}
            onBack={() => handleStepChange(ApplicationStep.DOCUMENT_UPLOAD)}
          />
        );
      case ApplicationStep.SUBMIT:
        return (
          <SubmitStep
            onNext={handleSubmitSubmit}
            onBack={() => handleStepChange(ApplicationStep.OVERVIEW)}
          />
        );
      default:
        return <StartStep onNext={handleNext} />;
    }
  };

  if (loading) {
    return (
      <div className={styles.loadingContainer}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className={styles.applicationFormPage}>
      <ApplicationHeader />
      <div className={styles.content}>{renderCurrentStep()}</div>
    </div>
  );
};

export default ApplicationForm;
