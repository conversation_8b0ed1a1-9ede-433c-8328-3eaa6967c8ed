import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import ApplicationForm from '../index';

// Mock the services
jest.mock('@/services/applicationForm', () => ({
  getApplicationForm: jest.fn().mockResolvedValue({
    success: true,
    data: { currentStep: 'START' }
  }),
  saveApplicationForm: jest.fn().mockResolvedValue({ success: true }),
  uploadFile: jest.fn().mockResolvedValue({
    success: true,
    data: { id: '1', name: 'test.pdf', url: '/test.pdf', status: 'COMPLETED' }
  }),
  getShareholderData: jest.fn().mockResolvedValue({
    success: true,
    data: [
      {
        id: '1',
        name: 'TAN WEI MING',
        type: 'INDIVIDUAL',
        status: 'COMPLETED'
      }
    ]
  })
}));

// Mock useParams
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ applicationId: 'test-123' }),
  useNavigate: () => jest.fn()
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <BrowserRouter>
      <ConfigProvider>
        {component}
      </ConfigProvider>
    </BrowserRouter>
  );
};

describe('ApplicationForm', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders application form with header', async () => {
    renderWithProviders(<ApplicationForm />);
    
    await waitFor(() => {
      expect(screen.getByText('Labuan Company Application')).toBeInTheDocument();
    });
  });

  test('displays start step initially', async () => {
    renderWithProviders(<ApplicationForm />);
    
    await waitFor(() => {
      expect(screen.getByText('Started Now')).toBeInTheDocument();
    });
  });

  test('can navigate to next step', async () => {
    renderWithProviders(<ApplicationForm />);
    
    await waitFor(() => {
      const startButton = screen.getByText('Started Now');
      fireEvent.click(startButton);
    });

    await waitFor(() => {
      expect(screen.getByText('Company Name')).toBeInTheDocument();
    });
  });

  test('validates required fields', async () => {
    renderWithProviders(<ApplicationForm />);
    
    // Navigate to company name step
    await waitFor(() => {
      const startButton = screen.getByText('Started Now');
      fireEvent.click(startButton);
    });

    // Try to submit without filling required fields
    await waitFor(() => {
      const nextButton = screen.getByText('Next');
      fireEvent.click(nextButton);
    });

    await waitFor(() => {
      expect(screen.getByText('Please enter company name in English')).toBeInTheDocument();
    });
  });
});
