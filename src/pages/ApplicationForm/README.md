# Labuan Company Application Form

## 项目概述

这是一个完整的拉布安公司申请表单系统，实现了多步骤表单流程，包含7个主要步骤。该系统独立于主布局运行，具有完整的状态管理、文件上传、表单验证等功能。

## 技术栈

- **React 19.1.0** - 前端框架
- **TypeScript** - 类型安全
- **Antd 5.26.6** - UI组件库
- **Tailwind CSS 3.4.0** - 样式框架
- **React Router DOM 7.7.1** - 路由管理
- **Vite** - 构建工具
- **CSS Modules + Less** - 样式解决方案

## 项目结构

```
src/pages/ApplicationForm/
├── index.tsx                           # 主容器组件
├── index.module.less                   # 主样式文件
├── components/                         # 组件目录
│   ├── StartStep.tsx                   # 步骤1：开始页面
│   ├── CompanyNameStep.tsx             # 步骤2：公司名称
│   ├── CompanyDetailsStep.tsx          # 步骤3：公司详情
│   ├── DocumentUploadStep.tsx          # 步骤4：文档上传
│   ├── ShareholderInfoStep.tsx         # 步骤5：股东信息
│   ├── IndividualShareholderStep.tsx   # 步骤6：个人股东详情
│   ├── CorporateShareholderStep.tsx    # 步骤7：公司股东详情
│   ├── ChecklistItem.tsx               # 清单项组件
│   ├── FormField.tsx                   # 通用表单字段
│   ├── PersonalInfoForm.tsx            # 个人信息表单
│   ├── RoleSelector.tsx                # 角色选择器
│   ├── PEPStatusSelector.tsx           # PEP状态选择器
│   └── *.module.less                   # 对应样式文件
├── __tests__/                          # 测试文件
│   └── ApplicationForm.test.tsx
├── TESTING_CHECKLIST.md               # 测试清单
└── README.md                          # 项目文档
```

## 功能特性

### 1. 多步骤表单流程
- **步骤1**：开始页面 - 显示注册清单和开始按钮
- **步骤2**：公司名称 - 英文和外语公司名称输入
- **步骤3**：公司详情 - 完整的公司信息表单
- **步骤4**：文档上传 - 支持拖拽上传和文件管理
- **步骤5**：股东信息 - 显示股东列表和状态
- **步骤6**：个人股东详情 - 个人KYC信息表单
- **步骤7**：公司股东详情 - 企业KYC信息表单

### 2. 状态管理
- 表单数据在步骤间持久化
- 支持前进和后退导航
- 当前步骤状态跟踪
- 股东编辑状态管理

### 3. 文件上传
- 拖拽上传支持
- 文件进度显示
- 文件预览和删除
- 多文件管理

### 4. 表单验证
- 实时字段验证
- 必填字段检查
- 格式验证（邮箱、电话等）
- 自定义验证规则

### 5. 响应式设计
- 桌面端优化布局
- 平板端自适应
- 移动端友好界面
- 断点：768px, 1024px

## 组件设计

### 可复用组件

#### FormField
通用表单字段组件，支持：
- 输入框、选择器、文本域、数字输入
- 统一的验证规则
- 响应式布局支持

#### PersonalInfoForm
个人信息表单组件，包含：
- 称谓、姓名、证件号码
- 出生日期、国籍
- 可配置显示字段

#### RoleSelector
角色选择组件，支持：
- 董事、股东、UBO角色
- 复选框卡片样式
- 可配置选项

#### PEPStatusSelector
PEP状态选择组件：
- 个人/企业类型适配
- 单选按钮组
- 自定义标签

### 步骤组件

每个步骤组件都遵循统一的接口：
```typescript
interface StepProps {
  onNext: (data: any) => void;
  onBack: () => void;
  initialData?: any;
}
```

## 路由配置

应用表单使用独立路由，不依赖MainLayout：

```typescript
// 支持两种路由模式
{
  path: "/application-form",        // 新建申请
  element: <ApplicationForm />,
},
{
  path: "/application/:applicationId", // 编辑现有申请
  element: <ApplicationForm />,
}
```

## 服务层

### API服务 (src/services/applicationForm.ts)
- `getApplicationForm()` - 获取申请表单数据
- `saveApplicationForm()` - 保存表单进度
- `uploadFile()` - 文件上传
- `getShareholderData()` - 获取股东数据

### 模拟数据
所有API都有完整的模拟实现，包含：
- 真实的延迟模拟
- 错误处理
- 数据验证
- 状态管理

## 类型定义

### 核心类型 (src/types/applicationForm.ts)
- `ApplicationStep` - 步骤枚举
- `ShareholderType` - 股东类型
- `CompanyNameFormData` - 公司名称数据
- `CompanyDetailsFormData` - 公司详情数据
- `DocumentUploadFormData` - 文档上传数据
- `ShareholderInfoFormData` - 股东信息数据
- `IndividualShareholderFormData` - 个人股东数据
- `CorporateShareholderFormData` - 企业股东数据

## 样式系统

### CSS Modules + Less
- 组件级样式隔离
- 响应式设计支持
- Antd主题定制
- 统一的设计令牌

### 设计系统
- 颜色：主色调 #1B3770，辅助色 #204184
- 字体：Inter字体系列
- 间距：8px基础网格系统
- 圆角：8px-12px标准圆角
- 阴影：统一的投影系统

## 开发指南

### 启动开发服务器
```bash
npm run dev
```

### 访问应用
- 开发环境：http://localhost:5174/application-form
- 生产环境：根据部署配置

### 添加新步骤
1. 在`ApplicationStep`枚举中添加新步骤
2. 创建步骤组件和样式文件
3. 在主容器的`renderCurrentStep`中添加路由
4. 更新类型定义和服务层

### 自定义样式
1. 使用CSS Modules避免样式冲突
2. 遵循响应式设计原则
3. 使用设计令牌保持一致性
4. 优先使用Antd组件样式

## 测试

### 手动测试
参考 `TESTING_CHECKLIST.md` 进行完整的功能测试

### 自动化测试
基础测试框架已配置，可扩展：
- 组件渲染测试
- 用户交互测试
- 表单验证测试
- 路由导航测试

## 部署注意事项

1. **路由配置**：确保服务器支持SPA路由
2. **文件上传**：配置正确的上传端点
3. **API集成**：替换模拟服务为真实API
4. **环境变量**：配置生产环境参数

## 维护指南

### 代码质量
- 使用TypeScript确保类型安全
- 遵循React最佳实践
- 保持组件单一职责
- 编写清晰的注释

### 性能优化
- 使用React.memo优化重渲染
- 懒加载大型组件
- 优化文件上传性能
- 监控内存使用

### 可访问性
- 使用语义化HTML
- 提供键盘导航支持
- 确保颜色对比度
- 添加ARIA标签

## 联系信息

如有问题或建议，请联系开发团队。
