# Application Form Testing Checklist

## Manual Testing Guide

### 1. 基本功能测试

#### 步骤一：开始页面
- [ ] 页面正确加载，显示ApplicationHeader
- [ ] 显示公司注册清单项目
- [ ] "Started Now"按钮可点击
- [ ] 点击后正确跳转到步骤二

#### 步骤二：公司名称
- [ ] 表单字段正确显示
- [ ] 英文公司名称必填验证
- [ ] 外语公司名称可选
- [ ] "Back"按钮返回步骤一
- [ ] "Next"按钮提交表单并跳转

#### 步骤三：公司详情
- [ ] 所有表单字段正确显示
- [ ] 必填字段验证工作正常
- [ ] 下拉选择器选项正确
- [ ] 表单提交和导航正常

#### 步骤四：文档上传
- [ ] 拖拽上传区域正常工作
- [ ] 文件选择器正常工作
- [ ] 上传进度显示正确
- [ ] 文件列表显示正确
- [ ] 删除文件功能正常

#### 步骤五：股东信息
- [ ] 股东卡片正确显示
- [ ] 不同状态（处理中、错误、完成）显示正确
- [ ] 编辑按钮跳转到详情页面
- [ ] 删除功能正常工作

#### 步骤六七：股东详情
- [ ] 个人股东表单正确显示
- [ ] 公司股东表单正确显示
- [ ] 表单验证正常工作
- [ ] 提交功能正常
- [ ] 返回按钮正常工作

### 2. 响应式设计测试

#### 桌面端 (>1024px)
- [ ] 布局正确显示
- [ ] 所有组件对齐正确
- [ ] 字体大小合适

#### 平板端 (768px - 1024px)
- [ ] 布局自适应调整
- [ ] 表单字段正确排列
- [ ] 按钮大小合适

#### 移动端 (<768px)
- [ ] 头部组件垂直排列
- [ ] 表单字段堆叠显示
- [ ] 按钮全宽显示
- [ ] 文字大小适中

### 3. 用户体验测试

#### 导航流程
- [ ] 步骤间导航流畅
- [ ] 返回按钮保持数据
- [ ] 进度状态正确显示

#### 表单交互
- [ ] 输入框焦点状态正确
- [ ] 错误提示清晰明确
- [ ] 成功提示及时显示
- [ ] 加载状态正确显示

#### 数据持久化
- [ ] 表单数据在步骤间保持
- [ ] 刷新页面后数据恢复
- [ ] 错误后数据不丢失

### 4. 性能测试

#### 加载性能
- [ ] 初始页面加载时间 < 3秒
- [ ] 步骤切换响应时间 < 1秒
- [ ] 文件上传进度流畅

#### 内存使用
- [ ] 长时间使用无内存泄漏
- [ ] 大文件上传不卡顿
- [ ] 多次导航无性能下降

### 5. 错误处理测试

#### 网络错误
- [ ] 网络断开时显示错误提示
- [ ] 重新连接后自动恢复
- [ ] 超时处理正确

#### 数据错误
- [ ] 无效数据提交被拦截
- [ ] 服务器错误正确显示
- [ ] 数据格式错误提示清晰

### 6. 浏览器兼容性测试

#### Chrome
- [ ] 所有功能正常
- [ ] 样式显示正确

#### Firefox
- [ ] 所有功能正常
- [ ] 样式显示正确

#### Safari
- [ ] 所有功能正常
- [ ] 样式显示正确

#### Edge
- [ ] 所有功能正常
- [ ] 样式显示正确

## 测试结果

### 发现的问题
1. 
2. 
3. 

### 修复状态
- [ ] 问题1已修复
- [ ] 问题2已修复
- [ ] 问题3已修复

### 测试完成确认
- [ ] 所有功能测试通过
- [ ] 响应式设计正确
- [ ] 用户体验良好
- [ ] 性能表现满足要求
- [ ] 错误处理完善
- [ ] 浏览器兼容性良好

测试人员：_____________
测试日期：_____________
测试版本：_____________
