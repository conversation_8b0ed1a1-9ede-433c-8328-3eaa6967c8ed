import React, { useState } from "react";
import { Form, Input, Button, App } from "antd";
import { useNavigate } from "react-router-dom";
import { login } from "@/services/auth";
import type { LoginParams } from "@/types/auth";
import styles from "./index.module.less";

// Logo图片常量
import imgBbsTransparentFinalizedLogoPng1 from "@/assets/logo-login.png";

const Login: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const navigate = useNavigate();

  // 使用 App hooks 获取 message API
  const { message } = App.useApp();

  const onFinish = async (values: LoginParams) => {
    setLoading(true);
    console.log("登录参数:", values); // 添加调试日志

    try {
      await login(values);
      message.success("登录成功！");
      // 跳转到应用页面
      navigate("/applications");
    } catch (error: any) {
      console.error("登录错误:", error); // 添加调试日志
      console.log("错误消息:", error.message); // 添加调试日志

      // 简化的错误消息显示
      message.error(error.message || "登录失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.loginContainer}>
      <div className={styles.loginCard}>
        {/* Logo */}
        <div className={styles.loginLogo}>
          <img
            src={imgBbsTransparentFinalizedLogoPng1}
            alt="BBS Logo"
            className={styles.logoImage}
          />
        </div>

        {/* 登录表单 */}
        <div className={styles.loginFormWrapper}>
          <Form
            form={form}
            name="login"
            onFinish={onFinish}
            autoComplete="off"
            layout="vertical"
            className={styles.loginForm}
          >
            {/* Email输入框 */}
            <Form.Item
              label="Email"
              name="username"
              rules={[
                {
                  required: true,
                  message: "请输入邮箱地址!",
                },
                {
                  type: "email",
                  message: "请输入有效的邮箱地址!",
                },
              ]}
              className={styles.formItem}
            >
              <Input
                placeholder="Enter email"
                className={styles.inputField}
                size="large"
              />
            </Form.Item>

            {/* Password输入框 */}
            <Form.Item
              label="Password"
              name="password"
              rules={[
                {
                  required: true,
                  message: "请输入密码!",
                },
              ]}
              className={styles.formItem}
            >
              <Input.Password
                placeholder="Enter password"
                className={styles.inputField}
                size="large"
              />
            </Form.Item>

            {/* 登录按钮 */}
            <Form.Item className={styles.formItemButton}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                className={styles.loginButton}
                size="large"
              >
                Login
              </Button>
            </Form.Item>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default Login;
