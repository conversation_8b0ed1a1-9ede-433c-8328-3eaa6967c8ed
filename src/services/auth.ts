import { post } from "@/utils/request";
import type {
  LoginParams,
  LoginResponse,
  RegisterParams,
  User,
} from "@/types/auth";

// 模拟用户数据
const mockUser: User = {
  id: "1",
  username: "admin",
  email: "<EMAIL>",
  avatar: "https://avatars.githubusercontent.com/u/1?v=4",
  role: "admin",
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
};

// 模拟token
const mockToken =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c";

/**
 * 用户登录
 */
export const login = async (params: LoginParams): Promise<LoginResponse> => {
  // 模拟API调用延迟
  await new Promise((resolve) => setTimeout(resolve, 1000));
  // 模拟登录验证
  if (params.username === "<EMAIL>" && params.password === "123123") {
    const response: LoginResponse = {
      token: mockToken,
      user: mockUser,
      expiresIn: 7200, // 2小时
    };

    // 保存token到localStorage
    localStorage.setItem("token", response.token);
    localStorage.setItem("user", JSON.stringify(response.user));

    return response;
  } else {
    throw new Error("用户名或密码错误");
  }
};

/**
 * 用户注册
 */
export const register = async (
  params: RegisterParams
): Promise<LoginResponse> => {
  // 模拟API调用延迟
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // 模拟注册验证
  if (params.password !== params.confirmPassword) {
    throw new Error("两次输入的密码不一致");
  }

  if (params.username === "admin") {
    throw new Error("用户名已存在");
  }

  const newUser: User = {
    ...mockUser,
    id: Date.now().toString(),
    username: params.username,
    email: params.email,
  };

  const response: LoginResponse = {
    token: mockToken,
    user: newUser,
    expiresIn: 7200,
  };

  // 保存token到localStorage
  localStorage.setItem("token", response.token);
  localStorage.setItem("user", JSON.stringify(response.user));

  return response;
};

/**
 * 获取当前用户信息
 */
export const getCurrentUser = async (): Promise<User> => {
  // 模拟API调用延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  const token = localStorage.getItem("token");
  if (!token) {
    throw new Error("未登录");
  }

  return mockUser;
};

/**
 * 用户登出
 */
export const logout = async (): Promise<void> => {
  // 模拟API调用延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  // 清除本地存储
  localStorage.removeItem("token");
  localStorage.removeItem("user");
};

/**
 * 刷新token
 */
export const refreshToken = async (): Promise<string> => {
  // 模拟API调用延迟
  await new Promise((resolve) => setTimeout(resolve, 500));

  const newToken = mockToken + "_refreshed_" + Date.now();
  localStorage.setItem("token", newToken);

  return newToken;
};
