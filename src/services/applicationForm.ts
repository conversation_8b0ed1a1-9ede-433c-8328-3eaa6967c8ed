import {
  type ApplicationFormData,
  type ApplicationFormResponse,
  type FileUploadResponse,
  type ShareholderResponse,
  type ShareholderInfo,
  type FileInfo,
  ApplicationStep,
  ShareholderType,
  UploadStatus,
} from "@/types/applicationForm";

// 模拟延迟
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// 生成唯一ID
const generateId = () => Math.random().toString(36).substr(2, 9);

// 模拟存储
let mockFormData: ApplicationFormData | null = null;
let mockShareholders: ShareholderInfo[] = [];

// 保存表单数据
export const saveApplicationForm = async (
  data: Partial<ApplicationFormData>
): Promise<ApplicationFormResponse> => {
  await delay(500);

  try {
    if (!mockFormData) {
      mockFormData = {
        id: generateId(),
        currentStep: ApplicationStep.START,
        companyName: {
          englishName: "",
          foreignName: "",
          alternativeName1: "",
          alternativeName2: "",
        },
        companyDetails: {
          businessNature: "",
          principalActivity: "",
          authorizedCapital: 0,
          paidUpCapital: 0,
          currencyType: "USD",
          registeredOfficeAddress: "",
          businessAddress: "",
          financialYearEnd: "",
          companySecretary: "",
          auditFirm: "",
        },
        uploadedDocuments: [],
        shareholders: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
    }

    // 更新数据
    mockFormData = {
      ...mockFormData,
      ...data,
      updatedAt: new Date().toISOString(),
    };

    return {
      success: true,
      data: mockFormData,
      message: "Form data saved successfully",
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to save form data",
      errors: [{ field: "general", message: "Server error occurred" }],
    };
  }
};

// 获取表单数据
export const getApplicationForm = async (
  id?: string
): Promise<ApplicationFormResponse> => {
  await delay(300);

  try {
    if (!mockFormData) {
      // 返回初始数据
      mockFormData = {
        id: id || generateId(),
        currentStep: ApplicationStep.START,
        companyName: {
          englishName: "",
          foreignName: "",
          alternativeName1: "",
          alternativeName2: "",
        },
        companyDetails: {
          businessNature: "",
          principalActivity: "",
          authorizedCapital: 0,
          paidUpCapital: 0,
          currencyType: "USD",
          registeredOfficeAddress: "",
          businessAddress: "",
          financialYearEnd: "",
          companySecretary: "",
          auditFirm: "",
        },
        uploadedDocuments: [],
        shareholders: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
    }

    return {
      success: true,
      data: mockFormData,
      message: "Form data retrieved successfully",
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to retrieve form data",
    };
  }
};

// 文件上传
export const uploadFile = async (file: File): Promise<FileUploadResponse> => {
  await delay(1000); // 模拟上传时间

  try {
    // 模拟文件验证
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return {
        success: false,
        message: "File size exceeds 10MB limit",
      };
    }

    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "application/pdf",
      "application/msword",
    ];
    if (!allowedTypes.includes(file.type)) {
      return {
        success: false,
        message: "File type not supported",
      };
    }

    const fileInfo: FileInfo = {
      id: generateId(),
      name: file.name,
      size: file.size,
      type: file.type,
      status: UploadStatus.SUCCESS,
      url: URL.createObjectURL(file), // 在实际项目中这里应该是服务器返回的URL
    };

    return {
      success: true,
      data: fileInfo,
      message: "File uploaded successfully",
    };
  } catch (error) {
    return {
      success: false,
      message: "File upload failed",
    };
  }
};

// 获取股东信息（模拟上传文档后返回的数据）
export const getShareholderInfo = async (): Promise<ShareholderResponse> => {
  await delay(800);

  try {
    // 模拟返回股东数据
    const mockShareholderData: ShareholderInfo[] = [
      {
        id: generateId(),
        type: ShareholderType.INDIVIDUAL,
        fullName: "John Smith",
        nationality: "United States",
        passportNumber: "US123456789",
        dateOfBirth: "1985-03-15",
        placeOfBirth: "New York, USA",
        residentialAddress: "123 Main St, New York, NY 10001",
        phoneNumber: "******-0123",
        email: "<EMAIL>",
        occupation: "Business Consultant",
        sharePercentage: 60,
        shareAmount: 60000,
        documents: [],
      },
      {
        id: generateId(),
        type: ShareholderType.CORPORATE,
        companyName: "Tech Solutions Ltd",
        registrationNumber: "*********",
        incorporationCountry: "Singapore",
        incorporationDate: "2020-01-15",
        registeredAddress: "1 Marina Bay, Singapore 018989",
        phoneNumber: "+65-6123-4567",
        email: "<EMAIL>",
        businessNature: "Technology Services",
        sharePercentage: 40,
        shareAmount: 40000,
        documents: [],
        authorizedRepresentative: {
          name: "Sarah Johnson",
          position: "Director",
          phoneNumber: "+65-6123-4568",
          email: "<EMAIL>",
        },
      },
    ];

    mockShareholders = mockShareholderData;

    return {
      success: true,
      data: mockShareholderData,
      message: "Shareholder information retrieved successfully",
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to retrieve shareholder information",
    };
  }
};

// 提交最终申请
export const submitApplication = async (
  data: ApplicationFormData
): Promise<ApplicationFormResponse> => {
  await delay(1500);

  try {
    // 模拟表单验证
    const errors = [];

    if (!data.companyName.englishName) {
      errors.push({
        field: "companyName.englishName",
        message: "English company name is required",
      });
    }

    if (!data.companyDetails.businessNature) {
      errors.push({
        field: "companyDetails.businessNature",
        message: "Business nature is required",
      });
    }

    if (data.uploadedDocuments.length === 0) {
      errors.push({
        field: "uploadedDocuments",
        message: "At least one document must be uploaded",
      });
    }

    if (data.shareholders.length === 0) {
      errors.push({
        field: "shareholders",
        message: "At least one shareholder is required",
      });
    }

    if (errors.length > 0) {
      return {
        success: false,
        message: "Validation failed",
        errors,
      };
    }

    // 模拟成功提交
    const submittedData = {
      ...data,
      submittedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockFormData = submittedData;

    return {
      success: true,
      data: submittedData,
      message: "Application submitted successfully",
    };
  } catch (error) {
    return {
      success: false,
      message: "Failed to submit application",
    };
  }
};

// 验证公司名称可用性
export const validateCompanyName = async (
  name: string
): Promise<{ available: boolean; message: string }> => {
  await delay(500);

  // 模拟名称验证逻辑
  const reservedNames = ["test", "admin", "system", "government"];
  const isReserved = reservedNames.some((reserved) =>
    name.toLowerCase().includes(reserved)
  );

  if (isReserved) {
    return {
      available: false,
      message: "This name contains reserved words and is not available",
    };
  }

  // 模拟随机可用性检查
  const isAvailable = Math.random() > 0.3; // 70% 概率可用

  return {
    available: isAvailable,
    message: isAvailable
      ? "Company name is available"
      : "Company name is already taken",
  };
};

// 获取股东数据（用于步骤五显示）
export const getShareholderData = async (): Promise<any[]> => {
  await delay(1000);

  // 模拟股东数据，包含不同状态
  const mockShareholders = [
    {
      id: "1",
      type: "individual",
      status: "processing",
      data: null,
      completion: 0,
    },
    {
      id: "2",
      type: "individual",
      status: "completed",
      data: {
        firstName: "John",
        lastName: "Doe",
        nricPassport: "123456-78-9012",
        dateOfBirth: "15 MAR 1985",
        nationality: "MALAYSIA",
      },
      completion: 3,
    },
    {
      id: "3",
      type: "individual",
      status: "error",
      data: null,
      errorMessage: "Document format not supported",
      completion: 0,
    },
    {
      id: "4",
      type: "corporate",
      status: "completed",
      data: {
        corporateName: "TECH SOLUTIONS SDN BHD",
        entityNo: "*********",
        incorporationDate: "20 JAN 2020",
        firstName: "Jane",
        lastName: "Smith",
        nricPassport: "987654-32-1098",
        dateOfBirth: "22 JUL 1980",
        nationality: "SINGAPORE",
      },
      completion: 100,
    },
  ];

  return mockShareholders;
};
