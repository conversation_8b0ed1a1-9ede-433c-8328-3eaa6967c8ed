// 申请表单相关类型定义

// 申请表单步骤枚举
export enum ApplicationStep {
  START = "start",
  COMPANY_NAME = "company_name",
  COMPANY_DETAILS = "company_details",
  DOCUMENT_UPLOAD = "document_upload",
  INDIVIDUAL_SHAREHOLDER = "individual_shareholder",
  CORPORATE_SHAREHOLDER = "corporate_shareholder",
  // 总览
  OVERVIEW = "overview",
  // 提交
  SUBMIT = "submit",
}

// 股东类型
export enum ShareholderType {
  INDIVIDUAL = "individual",
  CORPORATE = "corporate",
}

// 文件上传状态
export enum UploadStatus {
  PENDING = "pending",
  UPLOADING = "uploading",
  SUCCESS = "success",
  ERROR = "error",
}

// 公司名称表单数据
export interface CompanyNameForm {
  englishName: string;
  foreignName?: string;
  alternativeName1?: string;
  alternativeName2?: string;
}

// 公司详情表单数据
export interface CompanyDetailsForm {
  businessNature: string;
  principalActivity: string;
  authorizedCapital: number;
  paidUpCapital: number;
  currencyType: string;
  registeredOfficeAddress: string;
  businessAddress?: string;
  financialYearEnd: string;
  companySecretary: string;
  auditFirm?: string;
}

// 文件信息
export interface FileInfo {
  id: string;
  name: string;
  size: number;
  type: string;
  status: UploadStatus;
  url?: string;
  uploadProgress?: number;
  errorMessage?: string;
}

// 个人股东信息
export interface IndividualShareholder {
  id: string;
  type: ShareholderType.INDIVIDUAL;
  fullName: string;
  nationality: string;
  passportNumber: string;
  dateOfBirth: string;
  placeOfBirth: string;
  residentialAddress: string;
  mailingAddress?: string;
  phoneNumber: string;
  email: string;
  occupation: string;
  sharePercentage: number;
  shareAmount: number;
  documents: FileInfo[];
}

// 公司股东信息
export interface CorporateShareholder {
  id: string;
  type: ShareholderType.CORPORATE;
  companyName: string;
  registrationNumber: string;
  incorporationCountry: string;
  incorporationDate: string;
  registeredAddress: string;
  businessAddress?: string;
  phoneNumber: string;
  email: string;
  businessNature: string;
  sharePercentage: number;
  shareAmount: number;
  documents: FileInfo[];
  authorizedRepresentative: {
    name: string;
    position: string;
    phoneNumber: string;
    email: string;
  };
}

// 股东信息联合类型
export type ShareholderInfo = IndividualShareholder | CorporateShareholder;

// 申请表单完整数据
export interface ApplicationFormData {
  id?: string;
  currentStep: ApplicationStep;
  companyName: CompanyNameForm;
  companyDetails: CompanyDetailsForm;
  uploadedDocuments: FileInfo[];
  shareholders: ShareholderInfo[];
  createdAt?: string;
  updatedAt?: string;
  submittedAt?: string;
}

// 表单验证错误
export interface FormValidationError {
  field: string;
  message: string;
}

// API响应类型
export interface ApplicationFormResponse {
  success: boolean;
  data?: ApplicationFormData;
  message?: string;
  errors?: FormValidationError[];
}

// 文件上传响应
export interface FileUploadResponse {
  success: boolean;
  data?: FileInfo;
  message?: string;
}

// 股东信息响应
export interface ShareholderResponse {
  success: boolean;
  data?: ShareholderInfo[];
  message?: string;
}

// 表单步骤配置
export interface StepConfig {
  step: ApplicationStep;
  title: string;
  description?: string;
  isCompleted: boolean;
  isActive: boolean;
  canAccess: boolean;
}

// 申请表单上下文类型
export interface ApplicationFormContextType {
  formData: ApplicationFormData;
  currentStep: ApplicationStep;
  stepConfigs: StepConfig[];
  isLoading: boolean;
  errors: FormValidationError[];

  // 方法
  updateFormData: (data: Partial<ApplicationFormData>) => void;
  goToStep: (step: ApplicationStep) => void;
  nextStep: () => void;
  previousStep: () => void;
  submitForm: () => Promise<boolean>;
  uploadFile: (file: File) => Promise<FileInfo | null>;
  addShareholder: (shareholder: ShareholderInfo) => void;
  updateShareholder: (
    id: string,
    shareholder: Partial<ShareholderInfo>
  ) => void;
  removeShareholder: (id: string) => void;
  validateCurrentStep: () => boolean;
  clearErrors: () => void;
}

// 步骤表单数据类型
export type CompanyNameFormData = CompanyNameForm;
export type CompanyDetailsFormData = CompanyDetailsForm;

export interface DocumentUploadFormData {
  files: FileInfo[];
}

export interface ShareholderInfoFormData {
  shareholders: any[];
}

export interface IndividualShareholderFormData {
  firstName: string;
  lastName: string;
  nricPassport: string;
  dateOfBirth: string;
  nationality: string;
  address: string;
  phoneNumber: string;
  email: string;
  shareholdingPercentage: number;
  isDirector: boolean;
  isUBO: boolean;
}

export interface CorporateShareholderFormData {
  corporateName: string;
  entityNo: string;
  incorporationDate: string;
  address: string;
  phoneNumber: string;
  email: string;
  shareholdingPercentage: number;
  authorizedRepresentative: {
    firstName: string;
    lastName: string;
    nricPassport: string;
    dateOfBirth: string;
    nationality: string;
    position: string;
  };
}
