// 用户信息类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

// 登录请求参数
export interface LoginParams {
  username: string;
  password: string;
  remember?: boolean;
}

// 登录响应数据
export interface LoginResponse {
  token: string;
  user: User;
  expiresIn: number;
}

// 注册请求参数
export interface RegisterParams {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// 通用API响应格式
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}
