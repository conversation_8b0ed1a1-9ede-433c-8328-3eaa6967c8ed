// BackToList 组件样式
.backToList {
  padding: 2px;
  border: none;
  background: transparent;
  border-radius: 10px;
  cursor: pointer;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;

  &:hover {
    background: #f9fafb;
  }

  &:active {
    background: #f2f4f7;
  }

  // 图标容器
  .icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    color: #475467;

    svg {
      width: 20px;
      height: 20px;
    }

    // 加载动画
    .spinner {
      width: 12px;
      height: 12px;
      border: 2px solid #e5e7eb;
      border-top: 2px solid #475467;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  // 文本
  .text {
    font-family: "Inter", sans-serif;
    font-size: 14px;
    font-weight: 600;
    color: #344054;
    line-height: 18px;
    letter-spacing: 0.14px;
    white-space: nowrap;
  }

  // 禁用状态
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      background: transparent;
    }
  }

  // 加载状态
  &.loading {
    cursor: wait;
  }
}

// 加载动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .backToList {
    .text {
      font-size: 12px;
    }

    .icon {
      width: 18px;
      height: 18px;

      svg {
        width: 18px;
        height: 18px;
      }
    }
  }
}
