// ApplicationHeader 组件样式
.header {
  width: 100%;
  height: 93px;
  padding: 32px 80px 16px 80px;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid var(--Brand-Brown-500, #A97C50);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #FCFCFE;

  .logo {
    width: 83.33px;
    height: 45px;
  }

  .title {
    color: var(--Text-text-primary-(900), #081021);
    font-size: 24px;
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    line-height: 32px;
    letter-spacing: 0.24px;
    word-wrap: break-word;
  }

  .languageButton {
    width: 112px;
    padding: 8px 16px;
    background: var(--Button-button-secondary-color-bg, white);
    border-radius: 10px;
    border: 1px solid var(--Button-button-secondary-color-border, #5871A3);
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;

    span {
      flex: 1;
      color: var(--Button-button-secondary-color-fg, #1B3770);
      font-size: 14px;
      font-family: 'Inter', sans-serif;
      font-weight: 600;
      line-height: 18px;
      letter-spacing: 0.14px;
      word-wrap: break-word;
    }

    svg {
      width: 20px;
      height: 21px;
    }

    &:hover {
      border-color: #1B3770;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .header {
    padding: 16px 20px;
    height: auto;
    flex-direction: column;
    gap: 16px;

    .title {
      font-size: 20px;
      line-height: 24px;
      text-align: center;
    }

    .languageButton {
      width: auto;
      min-width: 100px;
    }
  }
}

@media (max-width: 480px) {
  .header {
    .title {
      font-size: 18px;
      line-height: 22px;
    }

    .logo {
      width: 70px;
      height: 38px;
    }
  }
}
