import React from 'react';
import { Button } from 'antd';
import styles from './index.module.less';

interface ApplicationHeaderProps {
  className?: string;
}

const ApplicationHeader: React.FC<ApplicationHeaderProps> = ({ className }) => {
  return (
    <div className={`${styles.header} ${className || ''}`}>
      <img 
        className={styles.logo} 
        src="/src/assets/bbs-register-logo.png" 
        alt="BBS Logo" 
      />
      <div className={styles.title}>
        Labuan Company Application
      </div>
      <Button className={styles.languageButton}>
        <span>English</span>
        <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M16.2052 7.47309C16.4506 7.27278 16.8129 7.28742 17.0418 7.51622C17.2706 7.74507 17.2852 8.10732 17.0849 8.3528L17.0418 8.40001L11.6088 13.8338C10.7231 14.7194 9.27677 14.7195 8.39105 13.8338L2.95811 8.40001L2.91497 8.3528C2.71472 8.10732 2.72927 7.74505 2.95811 7.51622C3.18694 7.28738 3.54921 7.27283 3.79469 7.47309L3.84189 7.51622L9.27565 12.9492C9.67324 13.3467 10.3274 13.3467 10.725 12.9492L16.158 7.51622L16.2052 7.47309Z" fill="var(--Icon-icon-fg-gray, #475467)"/>
        </svg>
      </Button>
    </div>
  );
};

export default ApplicationHeader;
