<div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
  <div style={{alignSelf: 'stretch', height: 24, color: 'var(--Text-text-primary-(900), #081021)', fontSize: 18, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.18, wordWrap: 'break-word'}}>Complete UBO Details</div>
  <div data-criminally="true" data-type="Form D" style={{width: 804, paddingTop: 80, paddingBottom: 24, paddingLeft: 24, paddingRight: 24, position: 'relative', background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 12, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'flex'}}>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 27, display: 'inline-flex'}}>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Filled" data-type="Dropdown" style={{width: 126, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Salutation</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Mr</div>
          </div>
          <div style={{width: 16, height: 16, position: 'relative'}}>
            <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
            <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
          </div>
        </div>
      </div>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Filled" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>First Name</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>WEI MING</div>
          </div>
        </div>
      </div>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Filled" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Last Name</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>TAN</div>
          </div>
        </div>
      </div>
    </div>
    <div style={{width: 742, justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
      <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Foreground-fg-quaternary-(500), #667085)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>NRIC/Passport Number</div>
        <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>*********</div>
      </div>
      <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Foreground-fg-quaternary-(500), #667085)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Nationality</div>
        <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Malaysian</div>
      </div>
      <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Foreground-fg-quaternary-(500), #667085)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Date of Birth</div>
        <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>12/09/1980</div>
      </div>
    </div>
    <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{width: 358, height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Percentage of shares</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>%</div>
          </div>
        </div>
      </div>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{width: 358, height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>UBO Percentage of shares</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>%</div>
          </div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Mobile Number</div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Mobile Number</div>
            </div>
          </div>
        </div>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Email </div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Email </div>
            </div>
          </div>
        </div>
      </div>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Occupation/Profession </div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Occupation/Profession </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style={{left: 609, top: 20, position: 'absolute', justifyContent: 'flex-start', alignItems: 'center', gap: 16, display: 'inline-flex'}}>
      <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="Secondary" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--Button-button-secondary-color-bg, white)', borderRadius: 10, outline: '1px var(--Button-button-secondary-color-border, #5871A3) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
        <div style={{width: 20, height: 20, position: 'relative', overflow: 'hidden'}}>
          <div style={{width: 15, height: 15, left: 2.50, top: 2.50, position: 'absolute', outline: '1.50px var(--Icon-icon-fg-gray, #475467) solid', outlineOffset: '-0.75px'}} />
        </div>
        <div style={{color: 'var(--Button-button-secondary-color-fg, #1B3770)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Re-Upload</div>
      </div>
      <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="IconButtonError" style={{padding: 4, borderRadius: 6, justifyContent: 'center', alignItems: 'center', display: 'flex'}}>
        <div style={{width: 18, height: 18, position: 'relative', overflow: 'hidden'}}>
          <div style={{width: 14.62, height: 16.13, left: 1.69, top: 0.94, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
        </div>
      </div>
    </div>
  </div>
</div>