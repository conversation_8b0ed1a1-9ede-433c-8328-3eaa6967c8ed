<div style={{width: 756, paddingLeft: 20, paddingRight: 20, paddingTop: 40, paddingBottom: 40, background: '#F4F7FF', borderRadius: 10, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 24, display: 'inline-flex'}}>
  <div style={{color: 'var(--Text-text-primary-(900), #081021)', fontSize: 20, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.20, wordWrap: 'break-word'}}>Supporting Documents</div>
  <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'flex'}}>
    <div data-state="Default" data-type="Single" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A certified copy of address proof issued within last 3 months (i.e utility bill, internet bill, credit card statement, etc.)</div>
      </div>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', padding: 12, background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select a file</div>
          </div>
        </div>
        <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="Secondary" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--Button-button-secondary-color-bg, white)', borderRadius: 10, outline: '1px var(--Button-button-secondary-color-border, #5871A3) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
          <div style={{width: 20, height: 20, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 15, height: 15, left: 2.50, top: 2.50, position: 'absolute', outline: '1.50px var(--Icon-icon-fg-gray, #475467) solid', outlineOffset: '-0.75px'}} />
          </div>
          <div style={{color: 'var(--Button-button-secondary-color-fg, #1B3770)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Browse file </div>
        </div>
      </div>
    </div>
    <div data-state="Default" data-type="Single" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A certified true copy of the latest annual return (or its equivalent) filed with the Registry of Companies/Commercial Registry/Financial Authority</div>
      </div>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', padding: 12, background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select a file</div>
          </div>
        </div>
        <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="Secondary" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--Button-button-secondary-color-bg, white)', borderRadius: 10, outline: '1px var(--Button-button-secondary-color-border, #5871A3) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
          <div style={{width: 20, height: 20, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 15, height: 15, left: 2.50, top: 2.50, position: 'absolute', outline: '1.50px var(--Icon-icon-fg-gray, #475467) solid', outlineOffset: '-0.75px'}} />
          </div>
          <div style={{color: 'var(--Button-button-secondary-color-fg, #1B3770)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Browse file </div>
        </div>
      </div>
    </div>
    <div data-state="Default" data-type="Single" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A certified true copy of the Memorandum & Articles of Association or Company By-Laws/Constitution</div>
      </div>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', padding: 12, background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select a file</div>
          </div>
        </div>
        <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="Secondary" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--Button-button-secondary-color-bg, white)', borderRadius: 10, outline: '1px var(--Button-button-secondary-color-border, #5871A3) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
          <div style={{width: 20, height: 20, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 15, height: 15, left: 2.50, top: 2.50, position: 'absolute', outline: '1.50px var(--Icon-icon-fg-gray, #475467) solid', outlineOffset: '-0.75px'}} />
          </div>
          <div style={{color: 'var(--Button-button-secondary-color-fg, #1B3770)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Browse file </div>
        </div>
      </div>
    </div>
    <div data-state="Default" data-type="Single" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A certified true copy of the Register of Directors and Members</div>
      </div>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', padding: 12, background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select a file</div>
          </div>
        </div>
        <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="Secondary" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--Button-button-secondary-color-bg, white)', borderRadius: 10, outline: '1px var(--Button-button-secondary-color-border, #5871A3) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
          <div style={{width: 20, height: 20, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 15, height: 15, left: 2.50, top: 2.50, position: 'absolute', outline: '1.50px var(--Icon-icon-fg-gray, #475467) solid', outlineOffset: '-0.75px'}} />
          </div>
          <div style={{color: 'var(--Button-button-secondary-color-fg, #1B3770)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Browse file </div>
        </div>
      </div>
    </div>
    <div data-state="Default" data-type="Single" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A Directors' resolution authorising the incorporation of the Labuan Company (corporate shareholder)</div>
      </div>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', padding: 12, background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select a file</div>
          </div>
        </div>
        <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="Secondary" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--Button-button-secondary-color-bg, white)', borderRadius: 10, outline: '1px var(--Button-button-secondary-color-border, #5871A3) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
          <div style={{width: 20, height: 20, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 15, height: 15, left: 2.50, top: 2.50, position: 'absolute', outline: '1.50px var(--Icon-icon-fg-gray, #475467) solid', outlineOffset: '-0.75px'}} />
          </div>
          <div style={{color: 'var(--Button-button-secondary-color-fg, #1B3770)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Browse file </div>
        </div>
      </div>
    </div>
    <div data-state="Default" data-type="Single" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Company ownership structure</div>
      </div>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', padding: 12, background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select a file</div>
          </div>
        </div>
        <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="Secondary" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--Button-button-secondary-color-bg, white)', borderRadius: 10, outline: '1px var(--Button-button-secondary-color-border, #5871A3) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
          <div style={{width: 20, height: 20, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 15, height: 15, left: 2.50, top: 2.50, position: 'absolute', outline: '1.50px var(--Icon-icon-fg-gray, #475467) solid', outlineOffset: '-0.75px'}} />
          </div>
          <div style={{color: 'var(--Button-button-secondary-color-fg, #1B3770)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Browse file </div>
        </div>
      </div>
    </div>
    <div data-state="Default" data-type="Single" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A certified true copy of the certificate of change of name (if any)</div>
      </div>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', padding: 12, background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select a file</div>
          </div>
        </div>
        <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="Secondary" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--Button-button-secondary-color-bg, white)', borderRadius: 10, outline: '1px var(--Button-button-secondary-color-border, #5871A3) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
          <div style={{width: 20, height: 20, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 15, height: 15, left: 2.50, top: 2.50, position: 'absolute', outline: '1.50px var(--Icon-icon-fg-gray, #475467) solid', outlineOffset: '-0.75px'}} />
          </div>
          <div style={{color: 'var(--Button-button-secondary-color-fg, #1B3770)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Browse file </div>
        </div>
      </div>
    </div>
    <div data-state="Default" data-type="Single" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A proof of Indirect Control over Labuan Entity (e.g. agreement, proof of ownership/control)</div>
      </div>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', padding: 12, background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select a file</div>
          </div>
        </div>
        <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="Secondary" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--Button-button-secondary-color-bg, white)', borderRadius: 10, outline: '1px var(--Button-button-secondary-color-border, #5871A3) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
          <div style={{width: 20, height: 20, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 15, height: 15, left: 2.50, top: 2.50, position: 'absolute', outline: '1.50px var(--Icon-icon-fg-gray, #475467) solid', outlineOffset: '-0.75px'}} />
          </div>
          <div style={{color: 'var(--Button-button-secondary-color-fg, #1B3770)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Browse file </div>
        </div>
      </div>
    </div>
  </div>
</div>