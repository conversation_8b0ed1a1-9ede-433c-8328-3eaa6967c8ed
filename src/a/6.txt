<div data-company="Labuan" data-type="Corporate" style={{width: 804, padding: 24, background: 'var(--Background-bg-primary, white)', borderRadius: 12, outline: '1px var(--Border-border-secondary, #E4E7EC) solid', outlineOffset: '-1px', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'inline-flex'}}>
  <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Filled" data-type="Textarea" style={{alignSelf: 'stretch', height: 117, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
    <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
      <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Representative Residential Address</div>
    </div>
    <div style={{alignSelf: 'stretch', flex: '1 1 0', paddingLeft: 12, paddingRight: 12, paddingTop: 8, paddingBottom: 8, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
      <div style={{flex: '1 1 0', alignSelf: 'stretch', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>A-21-12, Bulding ABC, Jalan KL, 51000 Kuala Lumpur</div>
    </div>
  </div>
  <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
    <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Authorized Representative Mobile Number</div>
      </div>
      <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter mobile no</div>
        </div>
      </div>
    </div>
    <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Authorized Representative Email</div>
      </div>
      <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter emal</div>
        </div>
      </div>
    </div>
  </div>
  <div data-criminally="false" data-type="Declaration" style={{width: 756, padding: 20, background: 'var(--Utility-Warning-utility-warning-50, #FFFAEC)', borderRadius: 10, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
    <div style={{color: 'var(--Text-text-primary-(900), #081021)', fontSize: 20, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.20, wordWrap: 'break-word'}}>Declaration</div>
    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Is this person a Politically Exposed Person (PEP)? [i.e., holds or has held public positions/functions]</div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
        </div>
        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Is this person affiliated or was affiliated with any Political Organization or individuals who are Politically Exposed Persons (PEPs)?</div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
        </div>
        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Has this person represented or previously represented any Political Organization or individuals who are Politically Exposed Persons (PEPs)?</div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
        </div>
        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
        </div>
      </div>
    </div>
  </div>
</div>