<div data-criminally="false" data-type="Declaration" style={{width: 756, padding: 20, background: 'var(--Utility-Warning-utility-warning-50, #FFFAEC)', borderRadius: 10, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'inline-flex'}}>
  <div style={{color: 'var(--Text-text-primary-(900), #081021)', fontSize: 20, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.20, wordWrap: 'break-word'}}>Declaration</div>
  <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
      <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Is this person a Politically Exposed Person (PEP)? [i.e., holds or has held public positions/functions]</div>
    </div>
    <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
        <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
        <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
      </div>
    </div>
  </div>
  <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
      <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Is this person affiliated or was affiliated with any Political Organization or individuals who are Politically Exposed Persons (PEPs)?</div>
    </div>
    <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
        <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
        <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
      </div>
    </div>
  </div>
  <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
      <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Has this person represented or previously represented any Political Organization or individuals who are Politically Exposed Persons (PEPs)?</div>
    </div>
    <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
        <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
        <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
      </div>
    </div>
  </div>
</div>