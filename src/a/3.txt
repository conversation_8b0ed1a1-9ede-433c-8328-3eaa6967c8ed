<div data-company="Labuan" data-type="Corporate" style={{width: 804, padding: 24, background: 'var(--Background-bg-primary, white)', borderRadius: 12, outline: '1px var(--Border-border-secondary, #E4E7EC) solid', outlineOffset: '-1px', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'inline-flex'}}>
  <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
    <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Tax Residency</div>
      </div>
      <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Tax residency</div>
        </div>
      </div>
    </div>
    <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>TIN</div>
      </div>
      <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Tax No</div>
        </div>
      </div>
    </div>
  </div>
  <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
    <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{flex: '1 1 0', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Estimated Net Worth</div>
      </div>
      <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select</div>
        </div>
        <div style={{width: 16, height: 16, position: 'relative'}}>
          <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
          <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
        </div>
      </div>
    </div>
    <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Annual Income/Annual Revenue</div>
      </div>
      <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select</div>
        </div>
        <div style={{width: 16, height: 16, position: 'relative'}}>
          <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
          <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
        </div>
      </div>
    </div>
  </div>
  <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
    <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{width: 358, height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Source of Wealth</div>
      </div>
      <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select</div>
        </div>
        <div style={{width: 16, height: 16, position: 'relative'}}>
          <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
          <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
        </div>
      </div>
    </div>
  </div>
  <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
      <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Nature of the Control over the Labuan Entity</div>
    </div>
    <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
        <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Direct</div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
        <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Indirect</div>
      </div>
    </div>
  </div>
  <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{alignSelf: 'stretch', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
    <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
      <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Type of Controlling Person</div>
    </div>
    <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
      <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select Type of Controlling Person</div>
      </div>
      <div style={{width: 16, height: 16, position: 'relative'}}>
        <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
        <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
      </div>
    </div>
  </div>
  <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{alignSelf: 'stretch', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
    <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
      <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Type of Beneficiary Owner</div>
    </div>
    <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
      <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select Type of Beneficiary Owner</div>
      </div>
      <div style={{width: 16, height: 16, position: 'relative'}}>
        <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
        <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
      </div>
    </div>
  </div>
</div>