<div data-criminally="true" data-type="Form D" style={{width: 804, paddingTop: 80, paddingBottom: 24, paddingLeft: 24, paddingRight: 24, background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 12, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'inline-flex'}}>
  <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'flex'}}>
    <div data-criminally="true" data-type="UploadContainer" style={{alignSelf: 'stretch', padding: 20, background: '#F4F7FF', borderRadius: 10, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
      <div style={{color: 'var(--Text-text-primary-(900), #081021)', fontSize: 20, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.20, wordWrap: 'break-word'}}>Supporting Documents</div>
      <div data-state="Uploaded 3" data-type="File upload 4" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
        <div style={{alignSelf: 'stretch', height: 200, paddingLeft: 24, paddingRight: 24, paddingTop: 16, paddingBottom: 16, background: 'var(--Background-bg-primary, white)', borderRadius: 12, outline: '1.50px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-0.75px', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
            <div data-color="Gray" data-gradient-mask="true" data-size="md" data-type="Modern" style={{width: 40, height: 40, position: 'relative', background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px'}}>
              <div style={{width: 20, height: 20, left: 10, top: 10, position: 'absolute', overflow: 'hidden'}}>
                <div style={{width: 16.67, height: 15, left: 1.67, top: 2.50, position: 'absolute', outline: '1.67px var(--Colors-Foreground-fg-secondary-(700), #CECFD2) solid', outlineOffset: '-0.83px'}} />
              </div>
            </div>
            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 4, display: 'inline-flex'}}>
                <div data-lefticon="false" data-righticon="false" data-size="S" data-state="Default" data-type="Tertiary" style={{padding: 2, borderRadius: 10, justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                  <div style={{color: 'var(--Foreground-fg-secondary-(700), #344054)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Click to upload</div>
                </div>
                <div style={{color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>or drag and drop</div>
              </div>
              <div style={{alignSelf: 'stretch', textAlign: 'center', color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>PNG, JPEG, PDF (max size file limit 5MB)</div>
            </div>
          </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
          <div data-show-dropdown="false" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Upload certified copy of address proof issued within last 3 months (i.e utility bill, internet bill, credit card statement, etc.)</div>
            </div>
          </div>
          <div data-show-dropdown="false" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Proof of Indirect Control over Labuan Entity (e.g. agreement, proof of ownership/control)</div>
            </div>
          </div>
          <div data-show-dropdown="true" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Provide supporting documents for individuals who have received the "Darjah Kebesaran" in Malaysia or any relevant documents for other salutations.</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div data-hint-text="true" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Filled" data-type="Textarea" style={{alignSelf: 'stretch', height: 117, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Residential Address</div>
      </div>
      <div style={{alignSelf: 'stretch', flex: '1 1 0', paddingLeft: 12, paddingRight: 12, paddingTop: 8, paddingBottom: 8, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', alignSelf: 'stretch', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>A-21-12, Bulding ABC, Jalan KL, 51000 Kuala Lumpur</div>
      </div>
      <div style={{alignSelf: 'stretch', height: 16, color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>This is a hint text to help user.</div>
    </div>
    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Correspondence address/Mailing Address </div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
        <div data-checked="True" data-size="sm" data-state="Default" data-type="Checkbox" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-solid, #1B3770)', overflow: 'hidden', borderRadius: 4}}>
          <div style={{width: 12, height: 12, left: 2, top: 2, position: 'absolute', overflow: 'hidden'}}>
            <div style={{width: 8, height: 5.50, left: 2, top: 3, position: 'absolute', outline: '1.67px var(--Foreground-fg-white, white) solid', outlineOffset: '-0.83px'}} />
          </div>
        </div>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Same as Residential Address</div>
      </div>
      <div style={{alignSelf: 'stretch', height: 107, paddingLeft: 12, paddingRight: 12, paddingTop: 8, paddingBottom: 8, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', borderRadius: 8, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
      <div style={{alignSelf: 'stretch', height: 16, color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>This is a hint text to help user.</div>
    </div>
  </div>
</div>