<div style={{width: 804, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 48, display: 'inline-flex'}}>
  <div style={{width: 350, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 24, display: 'flex'}}>
    <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="Tertiary" style={{padding: 2, borderRadius: 10, justifyContent: 'center', alignItems: 'center', gap: 4, display: 'inline-flex'}}>
      <div style={{width: 20, height: 20, position: 'relative'}}>
        <div style={{width: 12.92, height: 9.58, left: 3.54, top: 5.21, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
      </div>
      <div style={{color: 'var(--Foreground-fg-secondary-(700), #344054)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Back to List</div>
    </div>
    <div style={{alignSelf: 'stretch', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 24, fontFamily: 'Inter', fontWeight: '600', lineHeight: 32, letterSpacing: 0.24, wordWrap: 'break-word'}}>ABC Company - KYC Details</div>
  </div>
  <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 32, display: 'flex'}}>
    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Does this shareholder have an ultimate beneficial owner?</div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
        </div>
        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 18, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.18, wordWrap: 'break-word'}}>Kindly upload the documents of the ultimate beneficial owner.</div>
      <div style={{alignSelf: 'stretch', padding: 12, background: '#E7EFFF', borderRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
        <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>For Individual Directors and Shareholders:</div>
        <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 12, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>Malaysian: Upload a copy of Malaysian identity card (MyKad).</div>
        <div style={{alignSelf: 'stretch'}}><span style="color: 'var(--Brand-Blue-500, #204184)', fontSize: 12, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'">Foreigners</span><span style="color: 'var(--Brand-Blue-500, #204184)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'">: Upload a valid passport with a clear photo and signature.</span></div>
      </div>
      <div style={{alignSelf: 'stretch', padding: 12, background: '#E7EFFF', borderRadius: 8, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
        <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>For Corporate Directors & Shareholders:</div>
        <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>Upload a certified copy of the company’s Certificate of Incorporation.</div>
      </div>
    </div>
    <div data-state="Default" data-type="Multiple" style={{alignSelf: 'stretch', height: 200, paddingLeft: 24, paddingRight: 24, paddingTop: 16, paddingBottom: 16, background: 'var(--Background-bg-primary, white)', borderRadius: 12, outline: '1.50px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-0.75px', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
        <div data-color="Gray" data-gradient-mask="true" data-size="md" data-type="Modern" style={{width: 40, height: 40, position: 'relative', background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px'}}>
          <div style={{width: 20, height: 20, left: 10, top: 10, position: 'absolute', overflow: 'hidden'}}>
            <div style={{width: 16.67, height: 15, left: 1.67, top: 2.50, position: 'absolute', outline: '1.67px var(--Colors-Foreground-fg-secondary-(700), #CECFD2) solid', outlineOffset: '-0.83px'}} />
          </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 4, display: 'inline-flex'}}>
            <div data-lefticon="false" data-righticon="false" data-size="S" data-state="Default" data-type="Tertiary" style={{padding: 2, borderRadius: 10, justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{color: 'var(--Foreground-fg-secondary-(700), #344054)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Click to upload</div>
            </div>
            <div style={{color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>or drag and drop</div>
          </div>
          <div style={{alignSelf: 'stretch', textAlign: 'center', color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>PNG, JPEG, PDF (max size file limit 5MB)</div>
        </div>
      </div>
    </div>
  </div>
  <div data-company="Labuan" data-type="Corporate" style={{width: 804, padding: 24, background: 'var(--Background-bg-primary, white)', borderRadius: 12, outline: '1px var(--Border-border-secondary, #E4E7EC) solid', outlineOffset: '-1px', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'flex'}}>
    <div style={{alignSelf: 'stretch', height: 90, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Role in the Company</div>
      </div>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'inline-flex'}}>
        <div data-checkox="True" style={{minWidth: 189, paddingLeft: 16, paddingRight: 16, paddingTop: 20, paddingBottom: 20, background: 'var(--Background-bg-primary, white)', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="True" data-size="sm" data-state="Default" data-type="Checkbox" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-solid, #1B3770)', overflow: 'hidden', borderRadius: 4}}>
            <div style={{width: 12, height: 12, left: 2, top: 2, position: 'absolute', overflow: 'hidden'}}>
              <div style={{width: 8, height: 5.50, left: 2, top: 3, position: 'absolute', outline: '1.67px var(--Foreground-fg-white, white) solid', outlineOffset: '-0.83px'}} />
            </div>
          </div>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Director</div>
        </div>
        <div data-checkox="True" style={{minWidth: 189, paddingLeft: 16, paddingRight: 16, paddingTop: 20, paddingBottom: 20, background: 'var(--Background-bg-primary, white)', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="True" data-size="sm" data-state="Default" data-type="Checkbox" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-solid, #1B3770)', overflow: 'hidden', borderRadius: 4}}>
            <div style={{width: 12, height: 12, left: 2, top: 2, position: 'absolute', overflow: 'hidden'}}>
              <div style={{width: 8, height: 5.50, left: 2, top: 3, position: 'absolute', outline: '1.67px var(--Foreground-fg-white, white) solid', outlineOffset: '-0.83px'}} />
            </div>
          </div>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Shareholder</div>
        </div>
        <div data-checkox="True" style={{minWidth: 189, paddingLeft: 16, paddingRight: 16, paddingTop: 20, paddingBottom: 20, background: 'var(--Background-bg-primary, white)', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="True" data-size="sm" data-state="Default" data-type="Checkbox" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-solid, #1B3770)', overflow: 'hidden', borderRadius: 4}}>
            <div style={{width: 12, height: 12, left: 2, top: 2, position: 'absolute', overflow: 'hidden'}}>
              <div style={{width: 8, height: 5.50, left: 2, top: 3, position: 'absolute', outline: '1.67px var(--Foreground-fg-white, white) solid', outlineOffset: '-0.83px'}} />
            </div>
          </div>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Ultimate Beneficial Owners</div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
      <div style={{flex: '1 1 0', height: 68, justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'flex'}}>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Number & Percentage of share(s) to be issued</div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>%</div>
            </div>
          </div>
        </div>
      </div>
      <div style={{flex: '1 1 0', height: 68, justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'flex'}}>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>UBO Percentage of shares</div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Jurisdiction</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Jurisdiction</div>
          </div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Nature of Business</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Nature of Business</div>
          </div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'inline-flex'}}>
      <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'flex'}}>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Position in Labuan Entity</div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select Position</div>
            </div>
            <div style={{width: 16, height: 16, position: 'relative'}}>
              <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
              <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
            </div>
          </div>
        </div>
      </div>
      <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'flex'}}>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Email </div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Email </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Telephone Number</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Telephone Number </div>
          </div>
        </div>
      </div>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Mobile Number</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Mobile Number</div>
          </div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Tax Residency</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Tax residency</div>
          </div>
        </div>
      </div>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>TIN</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Tax No</div>
          </div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{flex: '1 1 0', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Estimated Net Worth</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select</div>
          </div>
          <div style={{width: 16, height: 16, position: 'relative'}}>
            <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
            <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
          </div>
        </div>
      </div>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Annual Income/Annual Revenue</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select</div>
          </div>
          <div style={{width: 16, height: 16, position: 'relative'}}>
            <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
            <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
          </div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{width: 358, height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Source of Wealth</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select</div>
          </div>
          <div style={{width: 16, height: 16, position: 'relative'}}>
            <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
            <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
          </div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Nature of the Control over the Labuan Entity</div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Direct</div>
        </div>
        <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
          <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Indirect</div>
        </div>
      </div>
    </div>
    <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{alignSelf: 'stretch', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Type of Controlling Person</div>
      </div>
      <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select Type of Controlling Person</div>
        </div>
        <div style={{width: 16, height: 16, position: 'relative'}}>
          <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
          <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
        </div>
      </div>
    </div>
    <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{alignSelf: 'stretch', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Type of Beneficiary Owner</div>
      </div>
      <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select Type of Beneficiary Owner</div>
        </div>
        <div style={{width: 16, height: 16, position: 'relative'}}>
          <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
          <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
        </div>
      </div>
    </div>
    <div data-criminally="true" data-type="UploadContainer" style={{alignSelf: 'stretch', padding: 20, background: '#F4F7FF', borderRadius: 10, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
      <div style={{color: 'var(--Text-text-primary-(900), #081021)', fontSize: 20, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.20, wordWrap: 'break-word'}}>Supporting Documents</div>
      <div data-state="Uploaded 3" data-type="File upload 4" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
        <div style={{alignSelf: 'stretch', height: 200, paddingLeft: 24, paddingRight: 24, paddingTop: 16, paddingBottom: 16, background: 'var(--Background-bg-primary, white)', borderRadius: 12, outline: '1.50px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-0.75px', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
            <div data-color="Gray" data-gradient-mask="true" data-size="md" data-type="Modern" style={{width: 40, height: 40, position: 'relative', background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px'}}>
              <div style={{width: 20, height: 20, left: 10, top: 10, position: 'absolute', overflow: 'hidden'}}>
                <div style={{width: 16.67, height: 15, left: 1.67, top: 2.50, position: 'absolute', outline: '1.67px var(--Colors-Foreground-fg-secondary-(700), #CECFD2) solid', outlineOffset: '-0.83px'}} />
              </div>
            </div>
            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 4, display: 'inline-flex'}}>
                <div data-lefticon="false" data-righticon="false" data-size="S" data-state="Default" data-type="Tertiary" style={{padding: 2, borderRadius: 10, justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                  <div style={{color: 'var(--Foreground-fg-secondary-(700), #344054)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Click to upload</div>
                </div>
                <div style={{color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>or drag and drop</div>
              </div>
              <div style={{alignSelf: 'stretch', textAlign: 'center', color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>PNG, JPEG, PDF (max size file limit 5MB)</div>
            </div>
          </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
          <div data-show-dropdown="true" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A certified copy of address proof issued within last 3 months (i.e utility bill, internet bill, credit card statement, etc.)</div>
            </div>
          </div>
          <div data-show-dropdown="false" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A certified true copy of the latest annual return (or its equivalent) filed with the Registry of Companies/Commercial Registry/Financial Authority</div>
            </div>
          </div>
          <div data-show-dropdown="false" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A certified true copy of the Memorandum & Articles of Association or Company By-Laws/Constitution</div>
            </div>
          </div>
          <div data-show-dropdown="true" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A certified true copy of the Register of Directors and Members</div>
            </div>
          </div>
          <div data-show-dropdown="true" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A Directors' resolution authorising the incorporation of the Labuan Company (corporate shareholder)</div>
            </div>
          </div>
          <div data-show-dropdown="true" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Company ownership structure</div>
            </div>
          </div>
          <div data-show-dropdown="true" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A certified true copy of the certificate of change of name (if any)</div>
            </div>
          </div>
          <div data-show-dropdown="true" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>A proof of Indirect Control over Labuan Entity (e.g. agreement, proof of ownership/control)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Filled" data-type="Textarea" style={{alignSelf: 'stretch', height: 117, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Address of Registered Office</div>
      </div>
      <div style={{alignSelf: 'stretch', flex: '1 1 0', paddingLeft: 12, paddingRight: 12, paddingTop: 8, paddingBottom: 8, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', alignSelf: 'stretch', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>A-21-12, Bulding ABC, Jalan KL, 51000 Kuala Lumpur</div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Correspondence address/Mailing Address </div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
        <div data-checked="True" data-size="sm" data-state="Default" data-type="Checkbox" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-solid, #1B3770)', overflow: 'hidden', borderRadius: 4}}>
          <div style={{width: 12, height: 12, left: 2, top: 2, position: 'absolute', overflow: 'hidden'}}>
            <div style={{width: 8, height: 5.50, left: 2, top: 3, position: 'absolute', outline: '1.67px var(--Foreground-fg-white, white) solid', outlineOffset: '-0.83px'}} />
          </div>
        </div>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Same as Registered Office Address</div>
      </div>
    </div>
    <div data-criminally="true" data-type="UploadContainer" style={{alignSelf: 'stretch', padding: 20, background: '#F4F7FF', borderRadius: 10, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
      <div style={{color: 'var(--Text-text-primary-(900), #081021)', fontSize: 20, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.20, wordWrap: 'break-word'}}>Authorized Representative Details</div>
      <div data-state="Uploaded 3" data-type="File upload 4" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
        <div style={{alignSelf: 'stretch', height: 200, paddingLeft: 24, paddingRight: 24, paddingTop: 16, paddingBottom: 16, background: 'var(--Background-bg-primary, white)', borderRadius: 12, outline: '1.50px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-0.75px', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
          <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
            <div data-color="Gray" data-gradient-mask="true" data-size="md" data-type="Modern" style={{width: 40, height: 40, position: 'relative', background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px'}}>
              <div style={{width: 20, height: 20, left: 10, top: 10, position: 'absolute', overflow: 'hidden'}}>
                <div style={{width: 16.67, height: 15, left: 1.67, top: 2.50, position: 'absolute', outline: '1.67px var(--Colors-Foreground-fg-secondary-(700), #CECFD2) solid', outlineOffset: '-0.83px'}} />
              </div>
            </div>
            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 4, display: 'inline-flex'}}>
                <div data-lefticon="false" data-righticon="false" data-size="S" data-state="Default" data-type="Tertiary" style={{padding: 2, borderRadius: 10, justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                  <div style={{color: 'var(--Foreground-fg-secondary-(700), #344054)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Click to upload</div>
                </div>
                <div style={{color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>or drag and drop</div>
              </div>
              <div style={{alignSelf: 'stretch', textAlign: 'center', color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>PNG, JPEG, PDF (max size file limit 5MB)</div>
            </div>
          </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
          <div data-show-dropdown="true" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Upload a copy of Malaysian identity card (MyKad)/ For foreigners upload Passport.</div>
            </div>
          </div>
          <div data-show-dropdown="false" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
              <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Upload certified copy of address proof issued within last 3 months (i.e utility bill, internet bill, credit card statement, etc.)</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Disable" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Authorized Representative Name</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-disabled, #F2F4F7)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-disabled, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>TAN WEN LAI</div>
          </div>
        </div>
      </div>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Disable" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Authorized Representative NRIC/Passport No</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-disabled, #F2F4F7)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-disabled, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>999999-99-9999</div>
          </div>
        </div>
      </div>
    </div>
    <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Filled" data-type="Textarea" style={{alignSelf: 'stretch', height: 117, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
        <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Representative Residential Address</div>
      </div>
      <div style={{alignSelf: 'stretch', flex: '1 1 0', paddingLeft: 12, paddingRight: 12, paddingTop: 8, paddingBottom: 8, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', alignSelf: 'stretch', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>A-21-12, Bulding ABC, Jalan KL, 51000 Kuala Lumpur</div>
      </div>
    </div>
    <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Authorized Representative Mobile Number</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter mobile no</div>
          </div>
        </div>
      </div>
      <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Authorized Representative Email</div>
        </div>
        <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter emal</div>
          </div>
        </div>
      </div>
    </div>
    <div data-criminally="false" data-type="Declaration" style={{width: 756, padding: 20, background: 'var(--Utility-Warning-utility-warning-50, #FFFAEC)', borderRadius: 10, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
      <div style={{color: 'var(--Text-text-primary-(900), #081021)', fontSize: 20, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.20, wordWrap: 'break-word'}}>Declaration</div>
      <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
        <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Is this person a Politically Exposed Person (PEP)? [i.e., holds or has held public positions/functions]</div>
        </div>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
            <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
          </div>
          <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
            <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
          </div>
        </div>
      </div>
      <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
        <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Is this person affiliated or was affiliated with any Political Organization or individuals who are Politically Exposed Persons (PEPs)?</div>
        </div>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
            <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
          </div>
          <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
            <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
          </div>
        </div>
      </div>
      <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
        <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
          <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Has this person represented or previously represented any Political Organization or individuals who are Politically Exposed Persons (PEPs)?</div>
        </div>
        <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
            <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
          </div>
          <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
            <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
    <div style={{alignSelf: 'stretch', height: 24, color: 'var(--Text-text-primary-(900), #081021)', fontSize: 18, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.18, wordWrap: 'break-word'}}>Complete UBO Details</div>
    <div data-criminally="true" data-type="Form D" style={{width: 804, paddingTop: 80, paddingBottom: 24, paddingLeft: 24, paddingRight: 24, position: 'relative', background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 12, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'flex'}}>
      <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 27, display: 'inline-flex'}}>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Filled" data-type="Dropdown" style={{width: 126, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Salutation</div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Mr</div>
            </div>
            <div style={{width: 16, height: 16, position: 'relative'}}>
              <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
              <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
            </div>
          </div>
        </div>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Filled" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>First Name</div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>WEI MING</div>
            </div>
          </div>
        </div>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Filled" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Last Name</div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>TAN</div>
            </div>
          </div>
        </div>
      </div>
      <div style={{width: 742, justifyContent: 'flex-start', alignItems: 'center', gap: 24, display: 'inline-flex'}}>
        <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Foreground-fg-quaternary-(500), #667085)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>NRIC/Passport Number</div>
          <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>*********</div>
        </div>
        <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Foreground-fg-quaternary-(500), #667085)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Nationality</div>
          <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Malaysian</div>
        </div>
        <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Foreground-fg-quaternary-(500), #667085)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Date of Birth</div>
          <div style={{alignSelf: 'stretch', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>12/09/1980</div>
        </div>
      </div>
      <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{width: 358, height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Percentage of shares</div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>%</div>
            </div>
          </div>
        </div>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{width: 358, height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>UBO Percentage of shares</div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>%</div>
            </div>
          </div>
        </div>
      </div>
      <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 32, display: 'flex'}}>
        <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
          <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Mobile Number</div>
            </div>
            <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
                <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Mobile Number</div>
              </div>
            </div>
          </div>
          <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Email </div>
            </div>
            <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
                <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Email </div>
              </div>
            </div>
          </div>
        </div>
        <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
          <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Occupation/Profession </div>
            </div>
            <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
                <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Occupation/Profession </div>
              </div>
            </div>
          </div>
        </div>
        <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
          <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Tax Residency</div>
            </div>
            <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
                <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Tax residency</div>
              </div>
            </div>
          </div>
          <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="InputField" style={{flex: '1 1 0', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>TIN</div>
            </div>
            <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
                <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Enter Tax No</div>
              </div>
            </div>
          </div>
        </div>
        <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
          <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{flex: '1 1 0', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Estimated Net Worth</div>
            </div>
            <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
                <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select</div>
              </div>
              <div style={{width: 16, height: 16, position: 'relative'}}>
                <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
                <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
              </div>
            </div>
          </div>
          <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Annual Income/Annual Revenue</div>
            </div>
            <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
                <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select</div>
              </div>
              <div style={{width: 16, height: 16, position: 'relative'}}>
                <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
                <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
              </div>
            </div>
          </div>
        </div>
        <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'center', gap: 40, display: 'inline-flex'}}>
          <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{flex: '1 1 0', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Source of Wealth</div>
            </div>
            <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
                <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select</div>
              </div>
              <div style={{width: 16, height: 16, position: 'relative'}}>
                <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
                <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
              </div>
            </div>
          </div>
        </div>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{alignSelf: 'stretch', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Type of Controlling Person</div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select Type of Controlling Person</div>
            </div>
            <div style={{width: 16, height: 16, position: 'relative'}}>
              <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
              <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
            </div>
          </div>
        </div>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Default" data-type="Dropdown" style={{alignSelf: 'stretch', height: 68, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Type of Beneficiary Owner</div>
          </div>
          <div style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'center', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Select Type of Beneficiary Owner</div>
            </div>
            <div style={{width: 16, height: 16, position: 'relative'}}>
              <div style={{width: 11.56, height: 5.73, left: 2.22, top: 5.47, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
              <div style={{width: 16, height: 16, left: 16, top: 16, position: 'absolute', transform: 'rotate(-180deg)', transformOrigin: 'top left', opacity: 0}} />
            </div>
          </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
          <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Nature of the Control over the Labuan Entity</div>
          </div>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
              <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Direct</div>
            </div>
            <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
              <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Indirect</div>
            </div>
          </div>
        </div>
        <div data-criminally="true" data-type="UploadContainer" style={{alignSelf: 'stretch', padding: 20, background: '#F4F7FF', borderRadius: 10, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
          <div style={{color: 'var(--Text-text-primary-(900), #081021)', fontSize: 20, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.20, wordWrap: 'break-word'}}>Supporting Documents</div>
          <div data-state="Uploaded 3" data-type="File upload 4" style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
            <div style={{alignSelf: 'stretch', height: 200, paddingLeft: 24, paddingRight: 24, paddingTop: 16, paddingBottom: 16, background: 'var(--Background-bg-primary, white)', borderRadius: 12, outline: '1.50px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-0.75px', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
              <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
                <div data-color="Gray" data-gradient-mask="true" data-size="md" data-type="Modern" style={{width: 40, height: 40, position: 'relative', background: 'var(--Background-bg-primary, white)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px'}}>
                  <div style={{width: 20, height: 20, left: 10, top: 10, position: 'absolute', overflow: 'hidden'}}>
                    <div style={{width: 16.67, height: 15, left: 1.67, top: 2.50, position: 'absolute', outline: '1.67px var(--Colors-Foreground-fg-secondary-(700), #CECFD2) solid', outlineOffset: '-0.83px'}} />
                  </div>
                </div>
                <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'center', gap: 4, display: 'flex'}}>
                  <div style={{alignSelf: 'stretch', justifyContent: 'center', alignItems: 'flex-start', gap: 4, display: 'inline-flex'}}>
                    <div data-lefticon="false" data-righticon="false" data-size="S" data-state="Default" data-type="Tertiary" style={{padding: 2, borderRadius: 10, justifyContent: 'center', alignItems: 'center', gap: 4, display: 'flex'}}>
                      <div style={{color: 'var(--Foreground-fg-secondary-(700), #344054)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Click to upload</div>
                    </div>
                    <div style={{color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>or drag and drop</div>
                  </div>
                  <div style={{alignSelf: 'stretch', textAlign: 'center', color: 'var(--Text-text-tertiary-(600), #475467)', fontSize: 12, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.12, wordWrap: 'break-word'}}>PNG, JPEG, PDF (max size file limit 5MB)</div>
                </div>
              </div>
            </div>
            <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
              <div data-show-dropdown="false" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
                <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
                  <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Upload certified copy of address proof issued within last 3 months (i.e utility bill, internet bill, credit card statement, etc.)</div>
                </div>
              </div>
              <div data-show-dropdown="false" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
                <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
                  <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Proof of Indirect Control over Labuan Entity (e.g. agreement, proof of ownership/control)</div>
                </div>
              </div>
              <div data-show-dropdown="true" data-showfile="false" data-state="File" style={{alignSelf: 'stretch', padding: 12, background: 'var(--Background-bg-primary, white)', borderRadius: 8, justifyContent: 'flex-start', alignItems: 'flex-end', gap: 12, display: 'inline-flex'}}>
                <div style={{flex: '1 1 0', flexDirection: 'column', justifyContent: 'center', alignItems: 'flex-start', gap: 8, display: 'inline-flex'}}>
                  <div style={{alignSelf: 'stretch', color: 'var(--Brand-Blue-500, #204184)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Provide supporting documents for individuals who have received the "Darjah Kebesaran" in Malaysia or any relevant documents for other salutations.</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div data-hint-text="false" data-label="true" data-left-icon="false" data-required="false" data-right-icon="false" data-state="Filled" data-type="Textarea" style={{alignSelf: 'stretch', height: 117, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Residential Address</div>
          </div>
          <div style={{alignSelf: 'stretch', flex: '1 1 0', paddingLeft: 12, paddingRight: 12, paddingTop: 8, paddingBottom: 8, background: 'var(--Background-bg-primary, white)', boxShadow: '0px 0.5px 1px rgba(24.73, 32.61, 60.56, 0.05)', overflow: 'hidden', borderRadius: 8, outline: '1px var(--Border-border-primary, #D0D5DD) solid', outlineOffset: '-1px', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', alignSelf: 'stretch', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>A-21-12, Bulding ABC, Jalan KL, 51000 Kuala Lumpur</div>
          </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 6, display: 'flex'}}>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Correspondence address/Mailing Address </div>
          </div>
          <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 8, display: 'inline-flex'}}>
            <div data-checked="True" data-size="sm" data-state="Default" data-type="Checkbox" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-solid, #1B3770)', overflow: 'hidden', borderRadius: 4}}>
              <div style={{width: 12, height: 12, left: 2, top: 2, position: 'absolute', overflow: 'hidden'}}>
                <div style={{width: 8, height: 5.50, left: 2, top: 3, position: 'absolute', outline: '1.67px var(--Foreground-fg-white, white) solid', outlineOffset: '-0.83px'}} />
              </div>
            </div>
            <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '400', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Same as Residential Address</div>
          </div>
        </div>
      </div>
      <div style={{left: 609, top: 20, position: 'absolute', justifyContent: 'flex-start', alignItems: 'center', gap: 16, display: 'inline-flex'}}>
        <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="Secondary" style={{paddingLeft: 16, paddingRight: 16, paddingTop: 8, paddingBottom: 8, background: 'var(--Button-button-secondary-color-bg, white)', borderRadius: 10, outline: '1px var(--Button-button-secondary-color-border, #5871A3) solid', outlineOffset: '-1px', justifyContent: 'center', alignItems: 'center', gap: 8, display: 'flex'}}>
          <div style={{width: 20, height: 20, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 15, height: 15, left: 2.50, top: 2.50, position: 'absolute', outline: '1.50px var(--Icon-icon-fg-gray, #475467) solid', outlineOffset: '-0.75px'}} />
          </div>
          <div style={{color: 'var(--Button-button-secondary-color-fg, #1B3770)', fontSize: 14, fontFamily: 'Inter', fontWeight: '600', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Re-Upload</div>
        </div>
        <div data-lefticon="true" data-righticon="false" data-size="S" data-state="Default" data-type="IconButtonError" style={{padding: 4, borderRadius: 6, justifyContent: 'center', alignItems: 'center', display: 'flex'}}>
          <div style={{width: 18, height: 18, position: 'relative', overflow: 'hidden'}}>
            <div style={{width: 14.62, height: 16.13, left: 1.69, top: 0.94, position: 'absolute', background: 'var(--Icon-icon-fg-gray, #475467)'}} />
          </div>
        </div>
      </div>
      <div data-criminally="false" data-type="Declaration" style={{width: 756, padding: 20, background: 'var(--Utility-Warning-utility-warning-50, #FFFAEC)', borderRadius: 10, flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 16, display: 'flex'}}>
        <div style={{color: 'var(--Text-text-primary-(900), #081021)', fontSize: 20, fontFamily: 'Inter', fontWeight: '500', lineHeight: 24, letterSpacing: 0.20, wordWrap: 'break-word'}}>Declaration</div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
          <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Is this person a Politically Exposed Person (PEP)? [i.e., holds or has held public positions/functions]</div>
          </div>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
              <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
            </div>
            <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
              <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
            </div>
          </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
          <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Is this person affiliated or was affiliated with any Political Organization or individuals who are Politically Exposed Persons (PEPs)?</div>
          </div>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
              <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
            </div>
            <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
              <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
            </div>
          </div>
        </div>
        <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 12, display: 'flex'}}>
          <div style={{alignSelf: 'stretch', justifyContent: 'flex-start', alignItems: 'flex-end', gap: 4, display: 'inline-flex'}}>
            <div style={{flex: '1 1 0', justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-primary-(900), #081021)', fontSize: 14, fontFamily: 'Inter', fontWeight: '500', lineHeight: 18, letterSpacing: 0.14, wordWrap: 'break-word'}}>Has this person represented or previously represented any Political Organization or individuals who are Politically Exposed Persons (PEPs)?</div>
          </div>
          <div style={{justifyContent: 'flex-start', alignItems: 'flex-start', gap: 40, display: 'inline-flex'}}>
            <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
              <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Yes</div>
            </div>
            <div style={{justifyContent: 'flex-start', alignItems: 'center', gap: 12, display: 'flex'}}>
              <div data-checked="False" data-size="sm" data-state="Default" data-type="Radio" style={{width: 16, height: 16, position: 'relative', background: 'var(--Background-bg-brand-primary, #DEE3ED)', borderRadius: 9999, border: '1px var(--Border-border-primary, #D0D5DD) solid'}} />
              <div style={{justifyContent: 'center', display: 'flex', flexDirection: 'column', color: 'var(--Text-text-placeholder, #667085)', fontSize: 16, fontFamily: 'Inter', fontWeight: '400', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>No</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div style={{alignSelf: 'stretch', flexDirection: 'column', justifyContent: 'flex-start', alignItems: 'flex-start', gap: 4, display: 'flex'}}>
    <div data-lefticon="false" data-righticon="false" data-size="M" data-state="Default" data-type="Primary" style={{alignSelf: 'stretch', paddingLeft: 20, paddingRight: 20, paddingTop: 12, paddingBottom: 12, background: 'var(--Button-button-primary-bg, #204184)', boxShadow: '0px 3px 4px rgba(223.02, 237.74, 255, 0.10) inset', borderRadius: 10, justifyContent: 'center', alignItems: 'center', gap: 12, display: 'inline-flex'}}>
      <div style={{color: 'var(--Button-button-primary-fg, white)', fontSize: 16, fontFamily: 'Inter', fontWeight: '600', lineHeight: 20, letterSpacing: 0.16, wordWrap: 'break-word'}}>Done</div>
    </div>
  </div>
</div>